/**
 * @note
 * logger
 *
 * <AUTHOR>
 * @date 	2025-09-05
 */
package elastic

import (
	"errors"
	"io"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/logger"
	"gitlab.docsl.com/security/common/masker"
)

type esCustomLogger struct {
	logger *hlog.Logger
}

// Printf log format string to error level
func (l *esCustomLogger) Printf(format string, args ...interface{}) {
	l.logger.Errorf(format, args...)
}

// LogRoundTrip should not modify the request or response, except for consuming and closing the body.
// Implementations have to check for nil values in request and response.
func (l *esCustomLogger) LogRoundTrip(req *http.Request, resp *http.Response, err error, time time.Time, cost time.Duration) error {
	if err == nil { // 无错误先不打日志
		return nil
	}
	var statusCode int
	var reqUrl, method string
	var reqBody, respBody []byte
	var reqBodyErr, respBodyErr error
	header := http.Header{}
	if req != nil {
		reqUrl = req.URL.String()
		method = req.Method
		for k, v := range req.Header {
			header[k] = v
		}
		if req.Body != nil {
			reqBody, reqBodyErr = io.ReadAll(req.Body)
			if reqBodyErr != nil {
				return errors.New("error reading request body: " + reqBodyErr.Error())
			}
		}
	}
	if resp != nil {
		statusCode = resp.StatusCode
		if resp.Body != nil {
			respBody, respBodyErr = io.ReadAll(resp.Body)
			if respBodyErr != nil {
				return errors.New("error reading response body: " + respBodyErr.Error())
			}
		}
	}
	//tag := common.LogTagElasticOK
	//if err != nil {
	tag := common.LogTagElasticErr
	err = common.NewError(common.ErrCodeElastic, err)
	//} else {
	//	err = common.NewError(common.ErrCodeOK)
	//}
	f := logrus.Fields{
		"tag":       tag,
		"proc_time": float64(cost.Nanoseconds() / 1e6),
		"url":       reqUrl,
		"req":       masker.MaskJsonString(l.logger, string(reqBody)),
		"resp":      masker.Mask(l.logger, string(respBody)),
		"header":    masker.Mask(l.logger, header),
		"method":    method,
		"httpCode":  statusCode,
	}
	logger.PrintLogWithError(err, l.logger, f)
	return nil
}

// RequestBodyEnabled makes the client pass a copy of request body to the logger.
func (l *esCustomLogger) RequestBodyEnabled() bool {
	return true
}

// ResponseBodyEnabled makes the client pass a copy of response body to the logger.
func (l *esCustomLogger) ResponseBodyEnabled() bool {
	return true
}
