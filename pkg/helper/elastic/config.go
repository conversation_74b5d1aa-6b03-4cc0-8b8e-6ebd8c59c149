/**
 * @note
 * config
 *
 * <AUTHOR>
 * @date 	2025-09-05
 */
package elastic

var (
	config Config
)

type Config struct {
	Endpoints            []string `json:"endpoints"` // elastic API entrypoints
	SSLCertValidation    bool     `json:"sslCertValidation,omitempty"`
	User                 string   `json:"user,omitempty" mask:"crypt"`     //basic auth user
	Password             string   `json:"password,omitempty" mask:"crypt"` //basic auth password
	NumWorkers           int      `json:"numWorkers,omitempty"`            // The number of worker goroutines
	FlushBytes           int      `json:"flushBytes,omitempty"`            // The flush threshold in bytes
	FlushIntervalSeconds int      `json:"flushIntervalSeconds,omitempty"`  // The periodic flush interval
}

func SetupConfig(c Config) {
	config = c
}

func GetConfig() Config {
	return config
}
