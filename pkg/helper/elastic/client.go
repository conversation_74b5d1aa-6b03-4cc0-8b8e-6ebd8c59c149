/**
 * @note
 * client
 *
 * <AUTHOR>
 * @date 	2025-09-05
 */
package elastic

import (
	"crypto/tls"
	elastic "github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/esutil"
	"github.com/tmsong/hlog"
	"gitlab.docsl.com/security/common"
	"net/http"
	"sync"
	"time"
)

var (
	client      *elastic.Client
	bulkIndexer esutil.BulkIndexer
	once        sync.Once
)

func InitClient(logger *hlog.Logger) (err error) {
	once.Do(func() {
		conf := GetConfig()
		if conf.NumWorkers <= 0 {
			conf.NumWorkers = 3
		}
		if conf.FlushBytes <= 0 {
			conf.FlushBytes = 5 << 20 // 默认 5 MB
		}
		if conf.FlushIntervalSeconds <= 0 {
			conf.FlushIntervalSeconds = 10 // 默认10秒
		}
		var transport http.RoundTripper
		if !conf.SSLCertValidation {
			transport = &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			}
		}
		esConf := elastic.Config{
			Addresses: conf.Endpoints,
			Logger:    &esCustomLogger{logger: logger},
		}
		if transport != nil {
			esConf.Transport = transport
		}
		if conf.User != common.StringEmpty && conf.Password != common.StringEmpty {
			esConf.Username = conf.User
			esConf.Password = conf.Password
		}
		if client, err = elastic.NewClient(esConf); err != nil {
			logger.Errorf("failed to create elastic client: %v", err)
			return
		}
		if bulkIndexer, err = esutil.NewBulkIndexer(esutil.BulkIndexerConfig{
			Client:        client,
			NumWorkers:    conf.NumWorkers,
			FlushBytes:    conf.FlushBytes,
			FlushInterval: time.Duration(conf.FlushIntervalSeconds) * time.Second,
		}); err != nil {
			logger.Errorf("failed to create elastic client: %v", err)
			return
		}
	})
	return err
}

func GetClient(logger *hlog.Logger) *elastic.Client {
	err := InitClient(logger)
	if err != nil {
		panic(err)
	}
	return client
}

func GetBulkIndexer(logger *hlog.Logger) esutil.BulkIndexer {
	err := InitClient(logger)
	if err != nil {
		panic(err)
	}
	return bulkIndexer
}
