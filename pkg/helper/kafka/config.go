/**
 * @note
 * config
 *
 * <AUTHOR>
 * @date 	2025-09-05
 */
package kafka

var (
	config Config
)

type Config struct {
	Version  string   `json:"version"`  // Kafka cluster version, eg: 0.10.2.0
	Brokers  []string `json:"brokers"`  // Kafka bootstrap brokers to connect to, as a comma separated list
	ClientID string   `json:"clientID"` // clientID

	// producer
	Partitioner string `json:"partitioner"` // producer partitioner
	Compression string `json:"compression"` // producer compression

	// consumer
	OffsetOldest           bool   `json:"offsetOldest"`                     // Kafka consumer consume initial offset from oldest
	MaxProcessingTimeMilli int    `json:"maxProcessingTimeMilli,omitempty"` // Kafka consumer max processing time
	Assignor               string `json:"assignor"`                         // Consumer group partition assignment strategy (range, roundrobin)
}

func SetupConfig(c Config) {
	config = c
}

func GetConfig() Config {
	return config
}
