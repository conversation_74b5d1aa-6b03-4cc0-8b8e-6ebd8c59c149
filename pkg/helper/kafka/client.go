/**
 * @note
 * client
 *
 * <AUTHOR>
 * @date 	2025-09-05
 */
package kafka

import (
	"github.com/IBM/sarama"
	"github.com/tmsong/hlog"
	"gitlab.docsl.com/security/common"
	"strings"
	"sync"
	"time"
)

var (
	once sync.Once
)

func NewProducer(logger *hlog.Logger) (sarama.AsyncProducer, error) {
	once.Do(func() {
		sarama.Logger = logger
	})
	conf := GetConfig()
	version, err := sarama.ParseKafkaVersion(conf.Version)
	if err != nil {
		return nil, err
	}
	sarConfig := sarama.NewConfig()
	sarConfig.Version = version
	sarConfig.ClientID = conf.ClientID
	if conf.Partitioner != common.StringEmpty {
		switch strings.ToLower(conf.Partitioner) {
		case "hash":
			sarConfig.Producer.Partitioner = sarama.NewHashPartitioner
		case "referencehash":
			sarConfig.Producer.Partitioner = sarama.NewReferenceHashPartitioner
		case "random":
			sarConfig.Producer.Partitioner = sarama.NewRandomPartitioner
		case "roundrobin":
			sarConfig.Producer.Partitioner = sarama.NewRoundRobinPartitioner
		}
	}

	if conf.Compression != common.StringEmpty {
		switch strings.ToLower(conf.Compression) {
		case "lz4":
			sarConfig.Producer.Compression = sarama.CompressionLZ4
		case "gzip":
			sarConfig.Producer.Compression = sarama.CompressionGZIP
		case "snappy":
			sarConfig.Producer.Compression = sarama.CompressionSnappy
		case "zstd":
			sarConfig.Producer.Compression = sarama.CompressionZSTD
		}
	}
	return sarama.NewAsyncProducer(conf.Brokers, sarConfig)
}

func NewConsumerGroup(logger *hlog.Logger, consumerGroup string) (sarama.ConsumerGroup, error) {
	once.Do(func() {
		sarama.Logger = logger
	})
	conf := GetConfig()
	version, err := sarama.ParseKafkaVersion(conf.Version)
	if err != nil {
		return nil, err
	}
	sarConfig := sarama.NewConfig()
	sarConfig.Version = version
	sarConfig.ClientID = conf.ClientID
	sarConfig.Metadata.Retry.Max = 16
	sarConfig.Metadata.Retry.BackoffFunc = func(retries, maxRetries int) time.Duration {
		if retries > maxRetries/2 {
			retries = maxRetries / 2
		}
		return (1 << retries) * 250 * time.Millisecond
	}

	if conf.MaxProcessingTimeMilli > 0 {
		sarConfig.Consumer.MaxProcessingTime = time.Duration(conf.MaxProcessingTimeMilli) * time.Millisecond
	}

	switch conf.Assignor {
	case "roundrobin":
		sarConfig.Consumer.Group.Rebalance.GroupStrategies = []sarama.BalanceStrategy{sarama.NewBalanceStrategyRoundRobin()}
	case "range":
		sarConfig.Consumer.Group.Rebalance.GroupStrategies = []sarama.BalanceStrategy{sarama.NewBalanceStrategyRange()}
	}
	return sarama.NewConsumerGroup(conf.Brokers, consumerGroup, sarConfig)
}
