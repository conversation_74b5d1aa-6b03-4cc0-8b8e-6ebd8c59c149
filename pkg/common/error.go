/**
 * @note
 * error
 *
 * <AUTHOR>
 * @date 	2025-02-05
 */
package common

import (
	"errors"
	"fmt"
)

var (
	ErrGormScanJsonAssertToBytes = errors.New("gorm scan json type assert to []byte failed")
	ErrAwsV4SignatureNotMatched  = errors.New("aws v4 signature not matched")
	ErrOptimisticLockFailed      = errors.New("optimistic lock failed: record has been modified by another process")
	ErrDeleteEnabledFilterRule   = errors.New("not allowed to delete enabled filter rule")
	ErrFilterRuleNotFound        = errors.New("filter rule not found")
	ErrDeduplicationRuleNotFound = errors.New("deduplication rule not found")
	ErrEvalRuleResultType        = errors.New("filter rule eval result not bool")
	ErrInvalidFilterRule         = func(rule interface{}) error {
		return fmt.Errorf("invalid filter rule %v", rule)
	}
	ErrInvalidFilterRuleOp = func(op string) error {
		return fmt.Errorf("invalid filter rule operator %s", op)
	}
	ErrInvalidFilterRuleStatus = errors.New("invalid filter rule status")
	ErrNoDraftToPublish        = errors.New("no draft rule to publish")
	ErrEnabledRuleNoDraft      = errors.New("enabled rule exists but no draft to publish")
)
