/**
 * @note
 * utils
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package common

import (
	"crypto/md5"
	"encoding/hex"
	"strconv"
	"time"
)

func ShortMD5(input string, length int) string {
	sum := md5.Sum([]byte(input))
	hexStr := hex.EncodeToString(sum[:])
	if length > len(hexStr) {
		length = len(hexStr)
	}
	return hexStr[:length]
}

func ShortID() string {
	return ShortMD5(strconv.FormatInt(time.Now().UnixNano(), 10), 10)
}
