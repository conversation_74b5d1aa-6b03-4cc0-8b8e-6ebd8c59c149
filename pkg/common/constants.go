package common

import (
	"os"
	"time"
)

var (
	Hostname string
)

const (
	ModuleName = "soc"

	DBName = "sec_soc"
)

func init() {
	Hostname, _ = os.Hostname()
	Hostname = ModuleName + "_" + Hostname
}

var (
	LocalLocation, _ = time.LoadLocation("Asia/Shanghai")
)

// table name
const (
	UserTableName                   = "soc_user"
	DepartmentTableName             = "soc_department"
	UserDepartmentRelationTableName = "soc_user_department_relation"

	OpLogTableName = "soc_operation_log"

	BizTableName        = "soc_biz"
	TenantTableName     = "soc_tenant"
	AlarmTypeTableName  = "soc_alarm_type"
	AlarmTableName      = "soc_alarm"
	FilterRuleTableName = "soc_filter_rule"
	BpmProcessTableName = "soc_bpm_process"

	UserCacheKeyFormat   = "soc_cache_user_%s"
	UserCacheExpirations = 120 * time.Second
)

const (
	OperationTenantCreate = "tenantCreate"
	OperationTenantUpdate = "tenantUpdate"
	OperationTenantDelete = "tenantDelete"

	OperationBizCreate = "bizCreate"
	OperationBizUpdate = "bizUpdate"
	OperationBizDelete = "bizDelete"

	OperationAlarmTypeCreate = "alarmTypeCreate"
	OperationAlarmTypeUpdate = "alarmTypeUpdate"
	OperationAlarmTypeDelete = "alarmTypeDelete"

	OperationFilterRuleCreate  = "filterRuleCreate"
	OperationFilterRuleUpdate  = "filterRuleUpdate"
	OperationFilterRuleDelete  = "filterRuleDelete"
	OperationFilterRulePublish = "filterRulePublish"
	OperationFilterRuleEdit    = "filterRuleEdit"
)

const TenantIDAll = "all"

type StatusEnum string

const (
	StatusNormal  StatusEnum = "Normal"  //正常状态
	StatusDeleted StatusEnum = "Deleted" //删除状态
)

type UserStatusEnum string

const (
	StatusActivated UserStatusEnum = "Activated" //激活状态
	StatusFrozen    UserStatusEnum = "Frozen"    //冻结状态
	StatusResigned  UserStatusEnum = "Resigned"  //离职状态
	StatusExited    UserStatusEnum = "Exited"    //主动退出状态
	StatusUnJoin    UserStatusEnum = "UnJoin"    //还未加入状态
)

type FilterRuleType string

const (
	TypeFilter        FilterRuleType = "Filter"
	TypeDeduplication FilterRuleType = "Deduplication"
)

type FilterRuleStatus string

const (
	StatusDraft    FilterRuleStatus = "Draft"
	StatusEnabled  FilterRuleStatus = "Enabled"
	StatusDisabled FilterRuleStatus = "Disabled"
	StatusArchived FilterRuleStatus = "Archived"
)

type BpmProcessStatus string

const (
	BpmStatusProcessing BpmProcessStatus = "Processing"
	BpmStatusSuccess    BpmProcessStatus = "Success"
	BpmStatusFailed     BpmProcessStatus = "Failed"
	BpmStatusWithdrawn  BpmProcessStatus = "Withdrawn" // 已被撤回
)

type BpmRefType string

const (
	BpmRefTypeFilterRule BpmRefType = "FilterRule"
)

type FilterOp string

const (
	OpEqual      = "=="
	OpNotEqual   = "!="
	OpIn         = "in"
	OpNotIn      = "notIn"
	OpExists     = "exists"
	OpNotExists  = "notExists"
	OpContain    = "contains"
	OpNotContain = "notContains"
	OpGt         = ">"
	OpGte        = ">="
	OpLt         = "<"
	OpLte        = "<="
)
