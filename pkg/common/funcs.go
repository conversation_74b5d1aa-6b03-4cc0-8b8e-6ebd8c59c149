/**
 * @note
 * funcs
 *
 * <AUTHOR>
 * @date 	2025-07-02
 */
package common

import (
	"encoding/json"
	"fmt"
	"github.com/tmsong/govaluate"
	"github.com/tsaikd/KDGoLib/errutil"
	"reflect"
	"strings"
)

var (
	ErrEvalCond                     = errutil.NewFactory("eval cond error")
	ErrArgsNumNotMatch              = errutil.NewFactory("args num not match")
	ErrArgsTypeNotMatch             = errutil.NewFactory("args type not match")
	ErrorBuiltInFunctionParameters1 = errutil.NewFactory("Built-in function '%s' parameters error")
	BuiltInFunctions                = map[string]govaluate.ExpressionFunction{
		"empty": func(args ...interface{}) (interface{}, error) {
			if len(args) > 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "empty")
			} else if len(args) == 0 {
				return true, nil
			}
			return args[0] == nil, nil
		},
		"strlen": func(args ...interface{}) (interface{}, error) {
			if len(args) > 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "strlen")
			} else if len(args) == 0 {
				return float64(0), nil
			}
			length := len(args[0].(string))
			return (float64)(length), nil
		},
		"strprefix": func(args ...interface{}) (interface{}, error) {
			if len(args) <= 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "strprefix")
			}
			src, ok := args[0].(string)
			if !ok {
				return false, nil
			}
			for i := 1; i < len(args); i++ {
				if tar, ok := args[i].(string); ok && strings.HasPrefix(src, tar) {
					return true, nil
				}
			}
			return false, nil
		},
		"strcontains": func(args ...interface{}) (interface{}, error) {
			if len(args) < 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "strcontains")
			}
			src, ok := args[0].(string)
			if !ok {
				return false, nil
			}
			for i := 1; i < len(args); i++ {
				if tar, ok := args[i].(string); ok && strings.Contains(src, tar) {
					return true, nil
				}
			}
			return false, nil
		},
		"contains": func(args ...interface{}) (interface{}, error) { //支持字符串或数字
			if len(args) < 2 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "contains")
			}
			target := args[len(args)-1]
			for i := 0; i < len(args)-1; i++ {
				if jn, ok := args[i].(json.Number); ok {
					if f, err := jn.Float64(); err != nil {
						return false, ErrorBuiltInFunctionParameters1.New(err, "contains")
					} else if f == target {
						return true, nil
					}
				} else if args[i] == target {
					return true, nil
				}
			}
			return false, nil
		},
		"in": func(args ...interface{}) (interface{}, error) { //支持字符串或数字
			if len(args) != 2 {
				return false, fmt.Errorf("in() expects 2 arguments")
			}
			// 第一个参数是要查找的元素
			needle := args[0]

			// 第二个参数是数组或切片
			haystack, ok := args[1].([]interface{})
			if !ok {
				return false, fmt.Errorf("second arg must be []interface{}")
			}

			for _, item := range haystack {
				if item == needle {
					return true, nil
				}
			}
			return false, nil
		},
		"map": func(args ...interface{}) (interface{}, error) {
			if len(args) > 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "map")
			} else if len(args) == 0 {
				return []interface{}{}, nil
			}

			s := reflect.ValueOf(args[0])
			if s.Kind() != reflect.Slice {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsTypeNotMatch, "map")
			}

			ret := make([]interface{}, s.Len())

			for i := 0; i < s.Len(); i++ {
				ret[i] = s.Index(i).Interface()
			}

			return ret, nil
		},
		"isnumeric": func(args ...interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "isnumeric")
			}
			kind := reflect.TypeOf(args[0]).Kind()
			return kind == reflect.Int || kind == reflect.Int8 || kind == reflect.Int16 || kind == reflect.Int32 || kind == reflect.Int64 ||
				kind == reflect.Uint || kind == reflect.Uint8 || kind == reflect.Uint16 || kind == reflect.Uint32 || kind == reflect.Uint64 ||
				kind == reflect.Float32 || kind == reflect.Float64, nil
		},
		"isstring": func(args ...interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "isstring")
			}
			kind := reflect.TypeOf(args[0]).Kind()
			return kind == reflect.String, nil
		},
		"length": func(args ...interface{}) (interface{}, error) {
			return float64(len(args)), nil
		},
	}
)
