/**
 * @note
 * errno
 *
 * <AUTHOR>
 * @date 	2025-02-05
 */
package common

import (
	"gitlab.docsl.com/security/common"
)

// 将特定的errno注入ErrnoDesc
func init() {
	for k, v := range ErrnoDesc {
		common.ErrnoDesc[k] = v
	}
}

const (
	ErrTriggerWorkflow = 10000

	// Biz相关错误码
	ErrCreateBiz    = 50001
	ErrUpdateBiz    = 50002
	ErrDeleteBiz    = 50003
	ErrQueryBiz     = 50004
	ErrQueryBizList = 50005

	// AlarmType相关错误码
	ErrCreateAlarmType    = 51001
	ErrUpdateAlarmType    = 51002
	ErrDeleteAlarmType    = 51003
	ErrQueryAlarmType     = 51004
	ErrQueryAlarmTypeList = 51005

	// Tenant相关错误码
	ErrCreateTenant    = 52001
	ErrUpdateTenant    = 52002
	ErrDeleteTenant    = 52003
	ErrQueryTenant     = 52004
	ErrQueryTenantList = 52005

	// FilterRule相关错误码
	ErrCreateFilterRule    = 53001
	ErrUpdateFilterRule    = 53002
	ErrDeleteFilterRule    = 53003
	ErrQueryFilterRule     = 53004
	ErrQueryFilterRuleList = 53005
	ErrPublishFilterRule   = 53006
	ErrEditFilterRule      = 53007
)

var ErrnoDesc = map[int]string{
	ErrTriggerWorkflow: "触发流程失败",

	// Biz相关错误描述
	ErrCreateBiz:    "创建业务失败",
	ErrUpdateBiz:    "更新业务失败",
	ErrDeleteBiz:    "删除业务失败",
	ErrQueryBiz:     "查询业务失败",
	ErrQueryBizList: "查询业务列表失败",

	// AlarmType相关错误描述
	ErrCreateAlarmType:    "创建告警类型失败",
	ErrUpdateAlarmType:    "更新告警类型失败",
	ErrDeleteAlarmType:    "删除告警类型失败",
	ErrQueryAlarmType:     "查询告警类型失败",
	ErrQueryAlarmTypeList: "查询告警类型列表失败",

	// Tenant相关错误描述
	ErrCreateTenant:    "创建租户失败",
	ErrUpdateTenant:    "更新租户失败",
	ErrDeleteTenant:    "删除租户失败",
	ErrQueryTenant:     "查询租户失败",
	ErrQueryTenantList: "查询租户列表失败",

	// FilterRule相关错误描述
	ErrCreateFilterRule:    "创建过滤规则失败",
	ErrUpdateFilterRule:    "更新过滤规则失败",
	ErrDeleteFilterRule:    "删除过滤规则失败",
	ErrQueryFilterRule:     "查询过滤规则失败",
	ErrQueryFilterRuleList: "查询过滤规则列表失败",
	ErrPublishFilterRule:   "发布过滤规则失败",
	ErrEditFilterRule:      "编辑过滤规则失败",
}
