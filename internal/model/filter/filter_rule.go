/**
 * @note
 * filter_rule
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package filter

import (
	"context"
	"fmt"
	"gorm.io/gorm"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

// FilterRule implementation methods
func (m *FilterRuleModelImpl) CreateFilterRule(ctx context.Context, tx *gorm.DB, rule *RuleTable) (int64, error) {
	rule.ID = uint(idgen.GetID())
	return int64(rule.ID), tx.Create(rule).Error
}

func (m *FilterRuleModelImpl) DeleteFilterRule(ctx context.Context, tx *gorm.DB, ruleID string) error {
	return tx.Where("rule_id = ?", ruleID).Delete(&RuleTable{}).Error
}

func (m *FilterRuleModelImpl) QueryLatestFilterRuleByRuleID(ctx context.Context, tx *gorm.DB, ruleID string, status []socCommon.FilterRuleStatus) (*RuleTable, error) {
	tx = tx.Model(&RuleTable{})
	ret := &RuleTable{}
	if len(status) > 0 {
		if len(status) == 1 {
			tx = tx.Where("status = ?", status[0])
		} else {
			tx = tx.Where("status IN ?", status)
		}
	}
	err := tx.Where("rule_id = ?", ruleID).Order("version DESC").First(&ret).Error
	return ret, err
}

func (m *FilterRuleModelImpl) UpdateFilterRuleByID(ctx context.Context, tx *gorm.DB, id uint, businessID *string, tenantID *TenantIDArray, name *string, desc *string, ruleType *socCommon.FilterRuleType, filterConfig *FilterRuleConfig, dedupConfig *DeduplicationRuleConfig, status *socCommon.FilterRuleStatus, version *int64) error {
	tx = tx.Model(&RuleTable{})
	updateMap := make(map[string]interface{})

	if businessID != nil {
		updateMap["business_id"] = *businessID
	}
	if tenantID != nil {
		updateMap["tenant_id"] = *tenantID
	}
	if name != nil {
		updateMap["name"] = *name
	}
	if desc != nil {
		updateMap["desc"] = *desc
	}
	if ruleType != nil {
		updateMap["type"] = *ruleType
	}
	if filterConfig != nil {
		updateMap["filter_config"] = *filterConfig
	}
	if dedupConfig != nil {
		updateMap["dedup_config"] = *dedupConfig
	}
	if status != nil {
		updateMap["status"] = *status
	}
	if version != nil {
		updateMap["version"] = *version
	}

	if len(updateMap) == 0 {
		return nil // 没有需要更新的字段
	}

	return tx.Where("id = ?", id).Updates(updateMap).Error
}

func (m *FilterRuleModelImpl) QueryFilterRuleBySeveralConditions(ctx context.Context, tx *gorm.DB, filter FilterRuleQueryFilter) ([]*RuleTable, error) {
	tx = tx.Model(&RuleTable{})

	// 分页
	if filter.PerPage > 0 {
		tx = tx.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		tx = tx.Offset((filter.Page - 1) * filter.PerPage)
	}

	tx = assembleFilterRuleQueryConditions(ctx, filter, tx)

	tbs := make([]*RuleTable, 0)
	if filter.Order != common.StringEmpty {
		tx = tx.Order(filter.Order)
	}
	tx = tx.Find(&tbs)
	return tbs, tx.Error
}

// QueryLatestFilterRule 查询最新版本的过滤规则
func (m *FilterRuleModelImpl) QueryLatestFilterRuleBySeveralConditions(ctx context.Context, tx *gorm.DB, filter FilterRuleQueryFilter) ([]*RuleTable, error) {
	tb := &RuleTable{}

	// 构建子查询：先select出rule_id和对应的max(version)
	subQuery := tx.Model(tb).
		Select("rule_id, MAX(version) as max_version").
		Group("rule_id")

	// 应用过滤条件到子查询
	subQuery = assembleFilterRuleQueryConditions(ctx, filter, subQuery)

	// 主查询：select出rule_id和version与子查询中结果相等的rule
	mainQuery := tx.Model(tb).
		Joins(fmt.Sprintf("INNER JOIN (?) as latest ON %s.rule_id = latest.rule_id AND %s.version = latest.max_version",
			tb.TableName(), tb.TableName()), subQuery)

	// 分页
	if filter.PerPage > 0 {
		mainQuery = mainQuery.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		mainQuery = mainQuery.Offset((filter.Page - 1) * filter.PerPage)
	}

	// 排序
	if filter.Order != common.StringEmpty {
		mainQuery = mainQuery.Order(filter.Order)
	}

	tbs := make([]*RuleTable, 0)
	err := mainQuery.Find(&tbs).Error
	return tbs, err
}

// QueryLatestFilterRuleCount 查询最新版本过滤规则的数量
func (m *FilterRuleModelImpl) QueryLatestFilterRuleCountBySeveralConditions(ctx context.Context, tx *gorm.DB, filter FilterRuleQueryFilter) (int64, error) {
	// 构建子查询：先select出rule_id和对应的max(version)
	subQuery := tx.Model(&RuleTable{}).
		Select("rule_id, MAX(version) as max_version").
		Group("rule_id")

	// 应用过滤条件到子查询
	subQuery = assembleFilterRuleQueryConditions(ctx, filter, subQuery)

	// 主查询：统计rule_id和version与子查询中结果相等的rule数量
	var count int64
	err := tx.Model(&RuleTable{}).
		Joins("INNER JOIN (?) as latest ON soc_filter_rule.rule_id = latest.rule_id AND soc_filter_rule.version = latest.max_version", subQuery).
		Count(&count).Error

	return count, err
}

func assembleFilterRuleQueryConditions(ctx context.Context, filter FilterRuleQueryFilter, db *gorm.DB) *gorm.DB {
	if len(filter.RuleIDs) > 0 {
		db = db.Where("rule_id IN ?", filter.RuleIDs)
	}
	if filter.BusinessID != common.StringEmpty {
		db = db.Where("business_id = ?", filter.BusinessID)
	}
	if len(filter.TenantIDs) > 0 {
		db = db.Where("JSON_OVERLAPS(tenant_id, ?)", filter.TenantIDs)
	}
	if filter.Name != common.StringEmpty {
		db = db.Where("name LIKE ?", "%"+filter.Name+"%")
	}
	if filter.Type != common.StringEmpty {
		db = db.Where("type = ?", filter.Type)
	}
	if filter.Status != common.StringEmpty {
		db = db.Where("status = ?", filter.Status)
	}
	if filter.Version != nil {
		db = db.Where("version = ?", filter.Version)
	}
	return db
}
