/**
 * @note
 * service
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package bpm

import (
	"context"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/mysql"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gorm.io/gorm"
)

type BpmModel interface {
	// BpmProcessTable methods
	CreateBpmProcess(ctx context.Context, bpmProcess *BpmProcessTable) (int64, error)
	DeleteBpmProcess(ctx context.Context, id uint) error
	UpdateBpmProcessByID(ctx context.Context, id uint, refID *string, refType *socCommon.BpmRefType, workflowID *string, initiator *string, detail *string, status *socCommon.BpmProcessStatus) error
	QueryBpmProcessBySeveralConditions(ctx context.Context, filter BpmProcessQueryFilter) ([]*BpmProcessTable, error)
	QueryBpmProcessCountBySeveralConditions(ctx context.Context, filter BpmProcessQueryFilter) (int64, error)
}

type BpmModelImpl struct{}

func (m *BpmModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(socCommon.DBName, false, common.GetLogger(ctx))
}

var DefaultService BpmModel = &BpmModelImpl{}

// Public functions for BpmProcessTable
func CreateBpmProcess(ctx context.Context, bpmProcess *BpmProcessTable) (int64, error) {
	return DefaultService.CreateBpmProcess(ctx, bpmProcess)
}

func DeleteBpmProcess(ctx context.Context, id uint) error {
	return DefaultService.DeleteBpmProcess(ctx, id)
}

func UpdateBpmProcessByID(ctx context.Context, id uint, refID *string, refType *socCommon.BpmRefType, workflowID *string, initiator *string, detail *string, status *socCommon.BpmProcessStatus) error {
	return DefaultService.UpdateBpmProcessByID(ctx, id, refID, refType, workflowID, initiator, detail, status)
}

func QueryBpmProcessBySeveralConditions(ctx context.Context, filter BpmProcessQueryFilter) ([]*BpmProcessTable, error) {
	return DefaultService.QueryBpmProcessBySeveralConditions(ctx, filter)
}

func QueryBpmProcessCountBySeveralConditions(ctx context.Context, filter BpmProcessQueryFilter) (int64, error) {
	return DefaultService.QueryBpmProcessCountBySeveralConditions(ctx, filter)
}
