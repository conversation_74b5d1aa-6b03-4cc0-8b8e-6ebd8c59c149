/**
 * @note
 * struct
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package bpm

import (
	"gorm.io/gorm"

	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

type BpmProcessTable struct {
	gorm.Model
	RefID      string                     `json:"refID" gorm:"column:ref_id"` // 审批所关联的资源ID
	RefType    socCommon.BpmRefType       `json:"refType" gorm:"column:ref_type"`
	WorkflowID string                     `json:"workflowID" gorm:"column:workflow_id"` // 对应的审批ID
	Initiator  string                     `json:"initiator" gorm:"column:initiator"`    // 发起人
	Detail     string                     `json:"detail" gorm:"column:detail"`
	Status     socCommon.BpmProcessStatus `json:"status" gorm:"column:status"`
}

func (t *BpmProcessTable) TableName() string {
	return socCommon.BpmProcessTableName
}

// Query filter struct
type BpmProcessQueryFilter struct {
	Page, PerPage int
	RefIDs        []string                       `json:"refIDs"`   // 支持传入RefID列表
	RefType       socCommon.BpmRefType           `json:"refType"`  // 引用类型
	Status        socCommon.BpmProcessStatus     `json:"status"`   // 状态
	WorkflowID    string                         `json:"workflowID"`
	Initiator     string                         `json:"initiator"`
	Order         string                         `json:"order"`
}
