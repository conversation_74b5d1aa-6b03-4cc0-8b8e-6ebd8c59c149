/**
 * @note
 * bmp_process
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package bpm

import (
	"context"
	"gitlab.docsl.com/security/common/idgen"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

// BpmProcessTable implementation methods
func (m *BpmModelImpl) CreateBpmProcess(ctx context.Context, bmpProcess *BpmProcessTable) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	bmpProcess.ID = uint(idgen.GetID())
	return int64(bmpProcess.ID), db.Create(bmpProcess).Error
}

func (m *BpmModelImpl) DeleteBpmProcess(ctx context.Context, id uint) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	return db.Where("id = ?", id).Delete(&BpmProcessTable{}).Error
}

func (m *BpmModelImpl) UpdateBpmProcessByID(ctx context.Context, id uint, refID *string, refType *socCommon.BpmRefType, workflowID *string, initiator *string, detail *string, status *socCommon.BpmProcessStatus) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}

	updates := make(map[string]interface{})
	if refID != nil {
		updates["ref_id"] = *refID
	}
	if refType != nil {
		updates["ref_type"] = *refType
	}
	if workflowID != nil {
		updates["workflow_id"] = *workflowID
	}
	if initiator != nil {
		updates["initiator"] = *initiator
	}
	if detail != nil {
		updates["detail"] = *detail
	}
	if status != nil {
		updates["status"] = *status
	}

	if len(updates) == 0 {
		return nil
	}

	return db.Model(&BpmProcessTable{}).Where("id = ?", id).Updates(updates).Error
}

func (m *BpmModelImpl) QueryBpmProcessBySeveralConditions(ctx context.Context, filter BpmProcessQueryFilter) ([]*BpmProcessTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}

	query := db.Model(&BpmProcessTable{})

	// 添加过滤条件
	if len(filter.RefIDs) > 0 {
		query = query.Where("ref_id IN ?", filter.RefIDs)
	}
	if filter.RefType != "" {
		query = query.Where("ref_type = ?", filter.RefType)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.WorkflowID != "" {
		query = query.Where("workflow_id = ?", filter.WorkflowID)
	}
	if filter.Initiator != "" {
		query = query.Where("initiator = ?", filter.Initiator)
	}

	// 添加排序
	if filter.Order != "" {
		query = query.Order(filter.Order)
	} else {
		query = query.Order("created_at DESC")
	}

	// 添加分页
	if filter.PerPage > 0 {
		offset := filter.Page * filter.PerPage
		query = query.Offset(offset).Limit(filter.PerPage)
	}

	var results []*BpmProcessTable
	err = query.Find(&results).Error
	return results, err
}

func (m *BpmModelImpl) QueryBpmProcessCountBySeveralConditions(ctx context.Context, filter BpmProcessQueryFilter) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}

	query := db.Model(&BpmProcessTable{})

	// 添加过滤条件
	if len(filter.RefIDs) > 0 {
		query = query.Where("ref_id IN ?", filter.RefIDs)
	}
	if filter.RefType != "" {
		query = query.Where("ref_type = ?", filter.RefType)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.WorkflowID != "" {
		query = query.Where("workflow_id = ?", filter.WorkflowID)
	}
	if filter.Initiator != "" {
		query = query.Where("initiator = ?", filter.Initiator)
	}

	var count int64
	err = query.Count(&count).Error
	return count, err
}
