/**
 * @note
 * conversion_example - 展示AlarmTable和AlarmEvent转换的使用示例
 *
 * <AUTHOR>
 * @date 	2025-09-08
 */
package alarm

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// ExampleConversion 展示转换方法的使用
func ExampleConversion() {
	fmt.Println("=== AlarmTable 和 AlarmEvent 转换示例 ===")

	// 1. 从数据库查询到的AlarmTable转换为AlarmEvent
	fmt.Println("\n1. AlarmTable -> AlarmEvent 转换:")
	
	// 模拟从数据库查询到的数据
	tableFromDB := &AlarmTable{
		Model: gorm.Model{
			ID:        1,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		AID:        "alarm-prod-001",
		Title:      "High CPU Usage Alert",
		Desc:       "Server CPU usage exceeded 90% threshold",
		Reporter:   "monitoring-system",
		TypeID:     "performance-alert",
		BusinessID: "web-service",
		TenantID:   "tenant-production",
		Topic:      "performance-alerts",
		// 注意：这些JSON字段保持了原始顺序
		Tags:       `{"severity":"high","component":"cpu","server":"web-01","datacenter":"us-east-1"}`,
		Detail:     `{"cpu_usage":92.5,"threshold":90,"duration":"5m","timestamp":"2025-09-08T14:30:00Z"}`,
		Context:    `{"processed":false,"assignee":"","escalated":false,"retry_count":0}`,
	}

	// 转换为AlarmEvent
	event, err := tableFromDB.ToAlarmEvent()
	if err != nil {
		fmt.Printf("转换失败: %v\n", err)
		return
	}

	fmt.Printf("转换成功! AID: %s\n", event.AID)
	fmt.Printf("Title: %s\n", event.Title)
	
	// 展示Raw字段保持了原始JSON顺序
	fmt.Printf("TagsRaw: %s\n", string(event.TagsRaw))
	fmt.Printf("DetailRaw: %s\n", string(event.DetailRaw))
	fmt.Printf("ContextRaw: %s\n", string(event.ContextRaw))
	
	// 展示反序列化后的字段可以直接使用
	fmt.Printf("Tags.severity: %s\n", event.Tags["severity"])
	fmt.Printf("Detail.cpu_usage: %v\n", event.Detail["cpu_usage"])
	fmt.Printf("Context.processed: %v\n", event.Context["processed"])

	// 2. 从外部接收的AlarmEvent转换为AlarmTable存储
	fmt.Println("\n2. AlarmEvent -> AlarmTable 转换:")
	
	// 模拟从外部API接收到的数据（保持JSON字段顺序）
	incomingEvent := &AlarmEvent{
		AID:        "alarm-api-002",
		Title:      "Database Connection Pool Exhausted",
		Desc:       "All database connections in pool are in use",
		Reporter:   "api-gateway",
		TypeID:     "database-alert",
		BusinessID: "user-service",
		TenantID:   "tenant-staging",
		Topic:      "database-alerts",
		// 使用json.RawMessage保持字段顺序
		TagsRaw:    json.RawMessage(`{"severity":"critical","component":"database","pool":"main","environment":"staging"}`),
		DetailRaw:  json.RawMessage(`{"pool_size":20,"active_connections":20,"queue_length":15,"wait_time":"30s"}`),
		ContextRaw: json.RawMessage(`{"auto_scale":true,"scale_threshold":18,"last_scale":"2025-09-08T14:25:00Z"}`),
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}

	// 转换为AlarmTable准备存储到数据库
	tableForDB, err := incomingEvent.ToAlarmTable()
	if err != nil {
		fmt.Printf("转换失败: %v\n", err)
		return
	}

	fmt.Printf("转换成功! AID: %s\n", tableForDB.AID)
	fmt.Printf("Title: %s\n", tableForDB.Title)
	
	// 展示转换后的JSON字符串字段
	fmt.Printf("Tags (string): %s\n", tableForDB.Tags)
	fmt.Printf("Detail (string): %s\n", tableForDB.Detail)
	fmt.Printf("Context (string): %s\n", tableForDB.Context)

	// 3. 往返转换示例
	fmt.Println("\n3. 往返转换示例 (Table -> Event -> Table):")
	
	originalTable := &AlarmTable{
		AID:        "alarm-roundtrip-003",
		Title:      "Memory Leak Detection",
		Desc:       "Potential memory leak detected in application",
		Reporter:   "memory-monitor",
		TypeID:     "memory-alert",
		BusinessID: "background-service",
		TenantID:   "tenant-dev",
		Topic:      "memory-alerts",
		Tags:       `{"severity":"medium","component":"memory","process":"worker","pid":12345}`,
		Detail:     `{"memory_usage":"2.5GB","growth_rate":"50MB/min","gc_frequency":"every_30s"}`,
		Context:    `{"investigation_started":false,"suspected_cause":"","monitoring_duration":"1h"}`,
	}

	// 第一步：Table -> Event
	intermediateEvent, err := originalTable.ToAlarmEvent()
	if err != nil {
		fmt.Printf("第一步转换失败: %v\n", err)
		return
	}

	// 第二步：Event -> Table
	finalTable, err := intermediateEvent.ToAlarmTable()
	if err != nil {
		fmt.Printf("第二步转换失败: %v\n", err)
		return
	}

	// 验证往返转换的一致性
	fmt.Printf("原始 AID: %s, 最终 AID: %s\n", originalTable.AID, finalTable.AID)
	fmt.Printf("原始 Tags: %s\n", originalTable.Tags)
	fmt.Printf("最终 Tags: %s\n", finalTable.Tags)
	fmt.Printf("JSON字段一致性: %v\n", originalTable.Tags == finalTable.Tags)

	// 4. 实际使用场景示例
	fmt.Println("\n4. 实际使用场景:")
	
	fmt.Println("场景A: 从数据库读取后发送到消息队列")
	fmt.Println("  1. 从数据库查询 AlarmTable")
	fmt.Println("  2. 调用 table.ToAlarmEvent() 转换")
	fmt.Println("  3. 将 AlarmEvent 序列化为JSON发送到Kafka")
	fmt.Println("  4. JSON保持原始字段顺序，便于下游系统处理")

	fmt.Println("\n场景B: 从外部API接收告警数据")
	fmt.Println("  1. 接收JSON数据反序列化为 AlarmEvent")
	fmt.Println("  2. 调用 event.ToAlarmTable() 转换")
	fmt.Println("  3. 将 AlarmTable 保存到数据库")
	fmt.Println("  4. Raw字段确保JSON格式和顺序不丢失")

	fmt.Println("\n场景C: 告警数据处理和转发")
	fmt.Println("  1. 从数据库读取 AlarmTable")
	fmt.Println("  2. 转换为 AlarmEvent 进行业务处理")
	fmt.Println("  3. 使用 event.Tags、event.Detail、event.Context 进行逻辑判断")
	fmt.Println("  4. 处理完成后转换回 AlarmTable 更新数据库")

	fmt.Println("\n=== 转换方法特点 ===")
	fmt.Println("✓ 保持JSON字段顺序 (使用json.RawMessage)")
	fmt.Println("✓ 自动反序列化便于使用 (Tags、Detail、Context字段)")
	fmt.Println("✓ 支持往返转换无数据丢失")
	fmt.Println("✓ 处理空字段情况")
	fmt.Println("✓ 错误处理和类型安全")
}
