/**
 * @note
 * conversion_test - 测试AlarmTable和AlarmEvent之间的转换
 *
 * <AUTHOR>
 * @date 	2025-09-08
 */
package alarm

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestAlarmTableToAlarmEvent(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	
	// 创建测试用的JSON数据
	tagsJSON := `{"severity":"high","component":"database","env":"production"}`
	detailJSON := `{"error_code":"DB_CONN_FAILED","timestamp":"2025-09-08T10:30:00Z","server_ip":"*************"}`
	contextJSON := `{"processed":false,"assignee":"","last_attempt":"2025-09-08T10:30:00Z"}`

	table := &AlarmTable{
		Model: gorm.Model{
			ID:        1,
			CreatedAt: now,
			UpdatedAt: now,
		},
		AID:        "alarm-test-001",
		Title:      "Database Connection Error",
		Desc:       "Unable to connect to database",
		Reporter:   "monitoring-system",
		TypeID:     "db-error",
		BusinessID: "user-service",
		TenantID:   "tenant-001",
		Topic:      "alerts",
		Tags:       tagsJSON,
		Detail:     detailJSON,
		Context:    contextJSON,
	}

	// 执行转换
	event, err := table.ToAlarmEvent()
	assert.NoError(t, err)
	assert.NotNil(t, event)

	// 验证基本字段
	assert.Equal(t, table.AID, event.AID)
	assert.Equal(t, table.Title, event.Title)
	assert.Equal(t, table.Desc, event.Desc)
	assert.Equal(t, table.Reporter, event.Reporter)
	assert.Equal(t, table.TypeID, event.TypeID)
	assert.Equal(t, table.BusinessID, event.BusinessID)
	assert.Equal(t, table.TenantID, event.TenantID)
	assert.Equal(t, table.Topic, event.Topic)
	assert.Equal(t, table.CreatedAt, event.CreateTime)
	assert.Equal(t, table.UpdatedAt, event.UpdateTime)

	// 验证Raw字段
	assert.Equal(t, json.RawMessage(tagsJSON), event.TagsRaw)
	assert.Equal(t, json.RawMessage(detailJSON), event.DetailRaw)
	assert.Equal(t, json.RawMessage(contextJSON), event.ContextRaw)

	// 验证反序列化的字段
	assert.Equal(t, "high", event.Tags["severity"])
	assert.Equal(t, "database", event.Tags["component"])
	assert.Equal(t, "production", event.Tags["env"])

	assert.Equal(t, "DB_CONN_FAILED", event.Detail["error_code"])
	assert.Equal(t, "2025-09-08T10:30:00Z", event.Detail["timestamp"])
	assert.Equal(t, "*************", event.Detail["server_ip"])

	assert.Equal(t, false, event.Context["processed"])
	assert.Equal(t, "", event.Context["assignee"])
	assert.Equal(t, "2025-09-08T10:30:00Z", event.Context["last_attempt"])
}

func TestAlarmEventToAlarmTable(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	
	// 创建有序的JSON数据（保持字段顺序）
	tagsRaw := json.RawMessage(`{"severity":"high","component":"database","env":"production"}`)
	detailRaw := json.RawMessage(`{"error_code":"DB_CONN_FAILED","timestamp":"2025-09-08T10:30:00Z","server_ip":"*************"}`)
	contextRaw := json.RawMessage(`{"processed":false,"assignee":"","last_attempt":"2025-09-08T10:30:00Z"}`)

	event := &AlarmEvent{
		AID:        "alarm-test-002",
		Title:      "Network Timeout",
		Desc:       "Network connection timeout",
		Reporter:   "network-monitor",
		TypeID:     "network-error",
		BusinessID: "api-service",
		TenantID:   "tenant-002",
		Topic:      "network-alerts",
		TagsRaw:    tagsRaw,
		DetailRaw:  detailRaw,
		ContextRaw: contextRaw,
		CreateTime: now,
		UpdateTime: now,
	}

	// 执行转换
	table, err := event.ToAlarmTable()
	assert.NoError(t, err)
	assert.NotNil(t, table)

	// 验证基本字段
	assert.Equal(t, event.AID, table.AID)
	assert.Equal(t, event.Title, table.Title)
	assert.Equal(t, event.Desc, table.Desc)
	assert.Equal(t, event.Reporter, table.Reporter)
	assert.Equal(t, event.TypeID, table.TypeID)
	assert.Equal(t, event.BusinessID, table.BusinessID)
	assert.Equal(t, event.TenantID, table.TenantID)
	assert.Equal(t, event.Topic, table.Topic)

	// 验证JSON字段转换为字符串
	assert.Equal(t, string(tagsRaw), table.Tags)
	assert.Equal(t, string(detailRaw), table.Detail)
	assert.Equal(t, string(contextRaw), table.Context)

	// 验证JSON格式正确性
	var tags map[string]string
	err = json.Unmarshal([]byte(table.Tags), &tags)
	assert.NoError(t, err)
	assert.Equal(t, "high", tags["severity"])

	var detail map[string]interface{}
	err = json.Unmarshal([]byte(table.Detail), &detail)
	assert.NoError(t, err)
	assert.Equal(t, "DB_CONN_FAILED", detail["error_code"])

	var context map[string]interface{}
	err = json.Unmarshal([]byte(table.Context), &context)
	assert.NoError(t, err)
	assert.Equal(t, false, context["processed"])
}

func TestRoundTripConversion(t *testing.T) {
	// 测试往返转换：AlarmTable -> AlarmEvent -> AlarmTable
	originalTable := &AlarmTable{
		Model: gorm.Model{
			ID:        1,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		AID:        "alarm-roundtrip-001",
		Title:      "Round Trip Test",
		Desc:       "Testing round trip conversion",
		Reporter:   "test-system",
		TypeID:     "test-error",
		BusinessID: "test-service",
		TenantID:   "test-tenant",
		Topic:      "test-alerts",
		Tags:       `{"level":"critical","source":"test"}`,
		Detail:     `{"test_id":"12345","message":"test message"}`,
		Context:    `{"test_flag":true,"counter":42}`,
	}

	// 第一次转换：Table -> Event
	event, err := originalTable.ToAlarmEvent()
	assert.NoError(t, err)
	assert.NotNil(t, event)

	// 第二次转换：Event -> Table
	resultTable, err := event.ToAlarmTable()
	assert.NoError(t, err)
	assert.NotNil(t, resultTable)

	// 验证往返转换后数据一致性
	assert.Equal(t, originalTable.AID, resultTable.AID)
	assert.Equal(t, originalTable.Title, resultTable.Title)
	assert.Equal(t, originalTable.Desc, resultTable.Desc)
	assert.Equal(t, originalTable.Reporter, resultTable.Reporter)
	assert.Equal(t, originalTable.TypeID, resultTable.TypeID)
	assert.Equal(t, originalTable.BusinessID, resultTable.BusinessID)
	assert.Equal(t, originalTable.TenantID, resultTable.TenantID)
	assert.Equal(t, originalTable.Topic, resultTable.Topic)
	assert.Equal(t, originalTable.Tags, resultTable.Tags)
	assert.Equal(t, originalTable.Detail, resultTable.Detail)
	assert.Equal(t, originalTable.Context, resultTable.Context)
}

func TestEmptyJSONFields(t *testing.T) {
	// 测试空JSON字段的处理
	table := &AlarmTable{
		AID:        "alarm-empty-001",
		Title:      "Empty Fields Test",
		Reporter:   "test-system",
		TypeID:     "test",
		BusinessID: "test",
		TenantID:   "test",
		// Tags, Detail, Context为空
	}

	event, err := table.ToAlarmEvent()
	assert.NoError(t, err)
	assert.NotNil(t, event)

	// 验证空字段处理
	assert.Nil(t, event.TagsRaw)
	assert.Nil(t, event.DetailRaw)
	assert.Nil(t, event.ContextRaw)
	assert.Nil(t, event.Tags)
	assert.Nil(t, event.Detail)
	assert.Nil(t, event.Context)

	// 反向转换
	resultTable, err := event.ToAlarmTable()
	assert.NoError(t, err)
	assert.Equal(t, "", resultTable.Tags)
	assert.Equal(t, "", resultTable.Detail)
	assert.Equal(t, "", resultTable.Context)
}
