/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-09-05
 */
package alarm

import (
	"gorm.io/gorm"
	"time"
)

type AlarmEvent struct {
	AID        string `json:"aid"`
	Title      string `json:"title"`
	Desc       string `json:"desc"` // 最大64K
	Reporter   string `json:"reporter"`
	TypeID     string `json:"type_id"`
	BusinessID string `json:"business_id"`
	TenantID   string `json:"tenant_id"`

	Topic string `json:"topic"` // 收到告警后，推到哪个kafka topic里，不填就不推

	Tags    map[string]string      `json:"tags"`
	Detail  map[string]interface{} `json:"detail"`  // 最大1M
	Context map[string]interface{} `json:"context"` // 最大1M

	CreateTime time.Time `json:"time"`
	UpdateTime time.Time `json:"update_time"`
}

type AlarmTable struct {
	gorm.Model
	AID        string `json:"aid" gorm:"column:aid"`
	Title      string `json:"title" gorm:"column:title"`
	Desc       string `json:"desc" gorm:"column:desc"` // 最大64K
	Reporter   string `json:"reporter" gorm:"column:reporter"`
	TypeID     string `json:"type_id" gorm:"column:type_id"`
	BusinessID string `json:"business_id" gorm:"column:business_id"`
	TenantID   string `json:"tenant_id" gorm:"column:tenant_id"`

	Topic string `json:"topic" gorm:"column:topic"` // 收到告警后，推到哪个kafka topic里，不填就不推

	Tags    string `json:"tags" gorm:"column:tags"`       // json格式
	Detail  string `json:"detail" gorm:"column:detail"`   // json格式，最大1M
	Context string `json:"context" gorm:"column:context"` // json格式，最大1M
}
