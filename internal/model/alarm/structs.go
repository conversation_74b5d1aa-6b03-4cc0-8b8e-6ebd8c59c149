/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-09-05
 */
package alarm

import (
	"encoding/json"
	"gorm.io/gorm"
	"time"

	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

type AlarmEvent struct {
	AID        string `json:"aid"`
	Title      string `json:"title"`
	Desc       string `json:"desc"` // 最大64K
	Reporter   string `json:"reporter"`
	TypeID     string `json:"type_id"`
	BusinessID string `json:"business_id"`
	TenantID   string `json:"tenant_id"`

	Topic string `json:"topic"` // 收到告警后，推到哪个kafka topic里，不填就不推

	Tags    map[string]string      `json:"-"`
	Detail  map[string]interface{} `json:"-"` // 最大1M
	Context map[string]interface{} `json:"-"` // 最大1M

	TagsRaw    json.RawMessage `json:"tags"`
	DetailRaw  json.RawMessage `json:"detail"`
	ContextRaw json.RawMessage `json:"context"`

	CreateTime time.Time `json:"time"`
	UpdateTime time.Time `json:"update_time"`
}

type AlarmTable struct {
	gorm.Model
	AID        string `json:"aid" gorm:"column:aid"`
	Title      string `json:"title" gorm:"column:title"`
	Desc       string `json:"desc" gorm:"column:desc"` // 最大64K
	Reporter   string `json:"reporter" gorm:"column:reporter"`
	TypeID     string `json:"type_id" gorm:"column:type_id"`
	BusinessID string `json:"business_id" gorm:"column:business_id"`
	TenantID   string `json:"tenant_id" gorm:"column:tenant_id"`

	Topic string `json:"topic" gorm:"column:topic"` // 收到告警后，推到哪个kafka topic里，不填就不推

	Tags    string `json:"tags" gorm:"column:tags"`       // json格式
	Detail  string `json:"detail" gorm:"column:detail"`   // json格式，最大1M
	Context string `json:"context" gorm:"column:context"` // json格式，最大1M
}

func (t *AlarmTable) TableName() string {
	return socCommon.AlarmTableName
}

// Query filter struct
type AlarmQueryFilter struct {
	Page, PerPage int
	AID           string `json:"aid"`
	TypeID        string `json:"typeID"`
	BusinessID    string `json:"businessID"`
	TenantID      string `json:"tenantID"`
	Title         string `json:"title"`
	Reporter      string `json:"reporter"`
	Order         string `json:"order"`
}

// ToAlarmEvent 将AlarmTable转换为AlarmEvent
func (t *AlarmTable) ToAlarmEvent() (*AlarmEvent, error) {
	event := &AlarmEvent{
		AID:        t.AID,
		Title:      t.Title,
		Desc:       t.Desc,
		Reporter:   t.Reporter,
		TypeID:     t.TypeID,
		BusinessID: t.BusinessID,
		TenantID:   t.TenantID,
		Topic:      t.Topic,
		CreateTime: t.CreatedAt,
		UpdateTime: t.UpdatedAt,
	}

	// 转换Tags字段
	if t.Tags != "" {
		event.TagsRaw = json.RawMessage(t.Tags)
		// 同时反序列化到Tags字段供使用
		if err := json.Unmarshal([]byte(t.Tags), &event.Tags); err != nil {
			return nil, err
		}
	}

	// 转换Detail字段
	if t.Detail != "" {
		event.DetailRaw = json.RawMessage(t.Detail)
		// 同时反序列化到Detail字段供使用
		if err := json.Unmarshal([]byte(t.Detail), &event.Detail); err != nil {
			return nil, err
		}
	}

	// 转换Context字段
	if t.Context != "" {
		event.ContextRaw = json.RawMessage(t.Context)
		// 同时反序列化到Context字段供使用
		if err := json.Unmarshal([]byte(t.Context), &event.Context); err != nil {
			return nil, err
		}
	}

	return event, nil
}

// ToAlarmTable 将AlarmEvent转换为AlarmTable
func (e *AlarmEvent) ToAlarmTable() (*AlarmTable, error) {
	table := &AlarmTable{
		AID:        e.AID,
		Title:      e.Title,
		Desc:       e.Desc,
		Reporter:   e.Reporter,
		TypeID:     e.TypeID,
		BusinessID: e.BusinessID,
		TenantID:   e.TenantID,
		Topic:      e.Topic,
	}

	// 使用Raw字段转换为字符串存储
	if len(e.TagsRaw) > 0 {
		table.Tags = string(e.TagsRaw)
	}

	if len(e.DetailRaw) > 0 {
		table.Detail = string(e.DetailRaw)
	}

	if len(e.ContextRaw) > 0 {
		table.Context = string(e.ContextRaw)
	}

	return table, nil
}
