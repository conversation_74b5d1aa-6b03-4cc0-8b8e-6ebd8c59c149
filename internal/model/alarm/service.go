/**
 * @note
 * service
 *
 * <AUTHOR>
 * @date 	2025-09-08
 */
package alarm

import (
	"context"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/mysql"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gorm.io/gorm"
)

type AlarmModel interface {
	// AlarmTable methods
	CreateAlarm(ctx context.Context, alarm *AlarmTable) (int64, error)
	DeleteAlarm(ctx context.Context, aid string) error
	UpdateAlarmContextByAID(ctx context.Context, aid string, context *string) error
	QueryAlarmBySeveralConditions(ctx context.Context, filter AlarmQueryFilter) ([]*AlarmTable, error)
	QueryAlarmCountBySeveralConditions(ctx context.Context, filter AlarmQueryFilter) (int64, error)
}

type AlarmModelImpl struct{}

func (m *AlarmModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(socCommon.DBName, false, common.GetLogger(ctx))
}

var DefaultService AlarmModel = &AlarmModelImpl{}

// Public functions for AlarmTable
func CreateAlarm(ctx context.Context, alarm *AlarmTable) (int64, error) {
	return DefaultService.CreateAlarm(ctx, alarm)
}

func DeleteAlarm(ctx context.Context, aid string) error {
	return DefaultService.DeleteAlarm(ctx, aid)
}

func UpdateAlarmContextByAID(ctx context.Context, aid string, context *string) error {
	return DefaultService.UpdateAlarmContextByAID(ctx, aid, context)
}

func QueryAlarmBySeveralConditions(ctx context.Context, filter AlarmQueryFilter) ([]*AlarmTable, error) {
	return DefaultService.QueryAlarmBySeveralConditions(ctx, filter)
}

func QueryAlarmCountBySeveralConditions(ctx context.Context, filter AlarmQueryFilter) (int64, error) {
	return DefaultService.QueryAlarmCountBySeveralConditions(ctx, filter)
}
