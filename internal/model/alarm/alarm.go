/**
 * @note
 * alarm
 *
 * <AUTHOR>
 * @date 	2025-09-08
 */
package alarm

import (
	"context"
	"gorm.io/gorm"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
)

// AlarmTable implementation methods
func (m *AlarmModelImpl) CreateAlarm(ctx context.Context, alarm *AlarmTable) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	alarm.ID = uint(idgen.GetID())
	return int64(alarm.ID), db.Create(alarm).Error
}

func (m *AlarmModelImpl) DeleteAlarm(ctx context.Context, aid string) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	return db.Where("aid = ?", aid).Delete(&AlarmTable{}).Error
}

func (m *AlarmModelImpl) UpdateAlarmContextByAID(ctx context.Context, aid string, context *string) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&AlarmTable{})
	updateMap := make(map[string]interface{})

	if context != nil {
		updateMap["context"] = *context
	}

	if len(updateMap) == 0 {
		return nil // 没有需要更新的字段
	}

	return db.Where("aid = ?", aid).Updates(updateMap).Error
}

func (m *AlarmModelImpl) QueryAlarmBySeveralConditions(ctx context.Context, filter AlarmQueryFilter) ([]*AlarmTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AlarmTable{})

	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleAlarmQueryConditions(ctx, filter, db)

	tbs := make([]*AlarmTable, 0)
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

func (m *AlarmModelImpl) QueryAlarmCountBySeveralConditions(ctx context.Context, filter AlarmQueryFilter) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&AlarmTable{})

	db = assembleAlarmQueryConditions(ctx, filter, db)
	var count int64
	db = db.Count(&count)
	return count, db.Error
}

func assembleAlarmQueryConditions(ctx context.Context, filter AlarmQueryFilter, db *gorm.DB) *gorm.DB {
	// 精确查询条件
	if filter.AID != common.StringEmpty {
		db = db.Where("aid = ?", filter.AID)
	}
	if filter.TypeID != common.StringEmpty {
		db = db.Where("type_id = ?", filter.TypeID)
	}
	if filter.BusinessID != common.StringEmpty {
		db = db.Where("business_id = ?", filter.BusinessID)
	}
	if filter.TenantID != common.StringEmpty {
		db = db.Where("tenant_id = ?", filter.TenantID)
	}

	// 模糊查询条件
	if filter.Title != common.StringEmpty {
		db = db.Where("title LIKE ?", "%"+filter.Title+"%")
	}
	if filter.Reporter != common.StringEmpty {
		db = db.Where("reporter LIKE ?", "%"+filter.Reporter+"%")
	}

	return db
}
