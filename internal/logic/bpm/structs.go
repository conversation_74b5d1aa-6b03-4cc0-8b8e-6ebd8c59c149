/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-09-04
 */
package bpm

import "time"

type BpmItem struct {
	ID         int64     `json:"id"`
	RefID      string    `json:"refID"`
	RefType    string    `json:"refType"`
	WorkflowID string    `json:"workflowID"`
	Initiator  string    `json:"initiator"`
	Detail     string    `json:"detail"`
	Status     string    `json:"status"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}