/**
 * @note
 * bpm
 *
 * <AUTHOR>
 * @date 	2025-09-04
 */
package bpm

import (
	"context"
	bpmModel "gitlab.docsl.com/security/socv2/soc/internal/model/bpm"
	"gitlab.docsl.com/security/socv2/soc/pkg/common"
)

func QueryProcessingBpmProcessListByRefIDsAndRefType(ctx context.Context, refIDs []string, refType common.BpmRefType) (items []*bpmModel.BpmProcessTable, err error) {
	filter := bpmModel.BpmProcessQueryFilter{
		RefIDs:  refIDs,
		RefType: refType,
		Status:  common.BpmStatusProcessing,
	}
	return bpmModel.QueryBpmProcessBySeveralConditions(ctx, filter)
}

// CreateBpm 创建BMP流程
func CreateBpm(ctx context.Context, refID string, refType common.BpmRefType, workflowID string, initiator string, detail string) (id int64, err error) {
	bmpProcess := &bpmModel.BpmProcessTable{
		RefID:      refID,
		RefType:    refType,
		WorkflowID: workflowID,
		Initiator:  initiator,
		Detail:     detail,
		Status:     common.BpmStatusProcessing, // 创建时默认为处理中状态
	}

	return bpmModel.CreateBpmProcess(ctx, bmpProcess)
}

// WithdrawBpm 撤回BPM流程
func WithdrawBpm(ctx context.Context, refID string, refType common.BpmRefType) error {
	// 查询该refID和refType对应的处理中的BMP流程
	filter := bpmModel.BpmProcessQueryFilter{
		RefIDs:  []string{refID},
		RefType: refType,
		Status:  common.BpmStatusProcessing,
	}

	bmpProcesses, err := bpmModel.QueryBpmProcessBySeveralConditions(ctx, filter)
	if err != nil {
		return err
	}

	// 如果没有找到处理中的流程，直接返回成功（幂等操作）
	if len(bmpProcesses) == 0 {
		return nil
	}

	// 将所有处理中的流程状态更新为已撤回
	withdrawnStatus := common.BpmStatusWithdrawn
	for _, bmpProcess := range bmpProcesses {
		err = bpmModel.UpdateBpmProcessByID(ctx, bmpProcess.ID, nil, nil, nil, nil, nil, &withdrawnStatus)
		if err != nil {
			return err
		}
	}

	return nil
}
