/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-09-08
 */
package engine

import (
	"github.com/tmsong/govaluate"
	filterModel "gitlab.docsl.com/security/socv2/soc/internal/model/filter"
	"gitlab.docsl.com/security/socv2/soc/pkg/common"
)

type CompiledFilterRule struct {
	RuleID           string                               `json:"ruleID"`
	BusinessID       string                               `json:"businessID"`
	TenantID         []string                             `json:"tenantID"`
	TypeID           string                               `json:"typeID"`
	Type             common.FilterRuleType                `json:"type"`
	FilterConfig     *filterModel.FilterRuleConfig        `json:"filter_config"`
	DedupConfig      *filterModel.DeduplicationRuleConfig `json:"dedupConfig"`
	filterCondition  string                               // 转化后的filterCondition
	filterExpression *govaluate.EvaluableExpression       // 转化后的expression
}
