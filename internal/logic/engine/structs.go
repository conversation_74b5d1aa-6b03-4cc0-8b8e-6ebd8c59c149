/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-09-08
 */
package engine

import (
	"github.com/tmsong/govaluate"
	filterModel "gitlab.docsl.com/security/socv2/soc/internal/model/filter"
	"gitlab.docsl.com/security/socv2/soc/pkg/common"
)

type CompiledFilterRule struct {
	Type             common.FilterRuleType                `json:"type"`
	FilterConfig     *filterModel.FilterRuleConfig        `json:"filter_config"`
	DedupConfig      *filterModel.DeduplicationRuleConfig `json:"dedupConfig"`
	filterExpression *govaluate.EvaluableExpression
}
