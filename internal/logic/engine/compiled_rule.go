/**
 * @note
 * engine
 *
 * <AUTHOR>
 * @date 	2025-09-04
 */
package engine

import (
	"context"
	"fmt"
	"github.com/tmsong/govaluate"
	"gitlab.docsl.com/security/common"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"strings"

	filterModel "gitlab.docsl.com/security/socv2/soc/internal/model/filter"
)

func CompileRule(ctx context.Context, tb *filterModel.RuleTable) (compiledRule *CompiledFilterRule, err error) {
	compiledRule = &CompiledFilterRule{
		Type:         tb.Type,
		FilterConfig: tb.FilterConfig,
		DedupConfig:  tb.DedupConfig,
	}
	// 先拼filterRule
	if compiledRule.FilterConfig != nil {
		if compiledRule.FilterConfig.RawRules != common.StringEmpty { // 如果已经指定了手写rawRule，那么直接使用
			compiledRule.filterCondition = compiledRule.FilterConfig.RawRules
		} else { // 否则拼
			compiledRule.filterCondition, err = buildConditionFromRules(compiledRule.FilterConfig.Rules)
		}
	}
	// 校验
	if len(compiledRule.filterCondition) == 0 && compiledRule.Type == socCommon.TypeFilter { // 说明没有filterRule
		return nil, socCommon.ErrFilterRuleNotFound
	} else if compiledRule.Type == socCommon.TypeDeduplication {
		if compiledRule.DedupConfig == nil ||
			len(compiledRule.DedupConfig.Keys) == 0 ||
			compiledRule.DedupConfig.TimeWindowSeconds <= 0 { // 如果去重规则也是空的，也有问题
			return nil, socCommon.ErrDeduplicationRuleNotFound
		}
	}
	// 编译
	compiledRule.filterExpression, err = govaluate.NewEvaluableExpressionWithFunctions(compiledRule.filterCondition, socCommon.BuiltInFunctions)
	if err != nil {
		return nil, err
	}
	return compiledRule, nil
}

// buildConditionFromRules 根据Rules构建条件表达式
// Rules是二维数组：第一层各[]FilterRule之间是或的关系，第二层各FilterRule之间是且的关系
func buildConditionFromRules(rules [][]filterModel.FilterRule) (string, error) {
	if len(rules) == 0 {
		return common.StringEmpty, nil // 没有规则时返回空
	}

	var orConditions []string

	// 遍历第一层（OR关系）
	for _, andRules := range rules {
		if len(andRules) == 0 { // 不允许传这种空的进来
			return common.StringEmpty, socCommon.ErrInvalidFilterRule(andRules)
		}

		var andConditions []string

		// 遍历第二层（AND关系）
		for _, rule := range andRules {
			condition, err := buildSingleCondition(rule)
			if err != nil {
				return common.StringEmpty, err
			}
			if condition != common.StringEmpty {
				andConditions = append(andConditions, condition)
			}
		}

		// 将AND条件组合
		if len(andConditions) > 0 {
			if len(andConditions) == 1 {
				orConditions = append(orConditions, andConditions[0])
			} else {
				orConditions = append(orConditions, "("+strings.Join(andConditions, " && ")+")")
			}
		} else { // 存在and条件是有问题的
			return common.StringEmpty, socCommon.ErrInvalidFilterRule(andRules)
		}
	}

	// 将OR条件组合
	if len(orConditions) == 0 {
		return common.StringEmpty, nil
	} else if len(orConditions) == 1 {
		return orConditions[0], nil
	} else {
		return "(" + strings.Join(orConditions, " || ") + ")", nil
	}
}

// buildSingleCondition 构建单个FilterRule的条件表达式
func buildSingleCondition(rule filterModel.FilterRule) (string, error) {
	field := rule.Field
	op := rule.Op
	value := rule.Value

	if field == common.StringEmpty || op == common.StringEmpty {
		return common.StringEmpty, socCommon.ErrInvalidFilterRule(rule)
	}

	switch op {
	case socCommon.OpEqual:
		return fmt.Sprintf("%s == %s", field, formatValue(value)), nil
	case socCommon.OpNotEqual:
		return fmt.Sprintf("%s != %s", field, formatValue(value)), nil
	case socCommon.OpGt:
		return fmt.Sprintf("%s > %s", field, formatValue(value)), nil
	case socCommon.OpGte:
		return fmt.Sprintf("%s >= %s", field, formatValue(value)), nil
	case socCommon.OpLt:
		return fmt.Sprintf("%s < %s", field, formatValue(value)), nil
	case socCommon.OpLte:
		return fmt.Sprintf("%s <= %s", field, formatValue(value)), nil
	case socCommon.OpIn:
		return fmt.Sprintf("in(%s, %s)", field, formatValue(value)), nil
	case socCommon.OpNotIn:
		return fmt.Sprintf("!in(%s, %s)", field, formatValue(value)), nil
	case socCommon.OpContain:
		return fmt.Sprintf("contains(%s, %s)", field, formatValue(value)), nil
	case socCommon.OpNotContain:
		return fmt.Sprintf("!contains(%s, %s)", field, formatValue(value)), nil
	case socCommon.OpExists:
		return fmt.Sprintf("exists(%s)", field), nil
	case socCommon.OpNotExists:
		return fmt.Sprintf("!exists(%s)", field), nil
	default:
		return common.StringEmpty, socCommon.ErrInvalidFilterRuleOp(op)
	}
}

// formatValue 格式化值，根据类型添加适当的引号或格式
func formatValue(value interface{}) string {
	if value == nil {
		return "null"
	}

	switch v := value.(type) {
	case string:
		// 字符串需要加引号
		return fmt.Sprintf(`"%s"`, strings.ReplaceAll(v, `"`, `\"`))
	case int, int8, int16, int32, int64:
		return fmt.Sprintf("%d", v)
	case uint, uint8, uint16, uint32, uint64:
		return fmt.Sprintf("%d", v)
	case float32, float64:
		return fmt.Sprintf("%g", v)
	case bool:
		return fmt.Sprintf("%t", v)
	case []interface{}:
		// 数组类型，用于in操作
		var items []string
		for _, item := range v {
			items = append(items, formatValue(item))
		}
		return "[" + strings.Join(items, ", ") + "]"
	default:
		// 其他类型转为字符串并加引号
		return fmt.Sprintf(`"%v"`, v)
	}
}
