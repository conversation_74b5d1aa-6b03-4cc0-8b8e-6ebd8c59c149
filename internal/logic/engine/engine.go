/**
 * @note
 * engine
 *
 * <AUTHOR>
 * @date 	2025-09-04
 */
package engine

import (
	"context"
	"github.com/tmsong/govaluate"
	"gitlab.docsl.com/security/common"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"

	filterModel "gitlab.docsl.com/security/socv2/soc/internal/model/filter"
)

func CompileRule(ctx context.Context, tb *filterModel.RuleTable) (compiledRule *CompiledFilterRule, err error) {
	compiledRule = &CompiledFilterRule{
		Type:         tb.Type,
		FilterConfig: tb.FilterConfig,
		DedupConfig:  tb.DedupConfig,
	}
	var condition string
	if compiledRule.FilterConfig != nil {
		if compiledRule.FilterConfig.RawRules != common.StringEmpty { // 如果已经指定了手写rawRule，那么直接使用
			condition = compiledRule.FilterConfig.RawRules
		} else { // 否则拼

		}
	}
	compiledRule.filterExpression, err = govaluate.NewEvaluableExpressionWithFunctions(condition, socCommon.BuiltInFunctions)
	if err != nil {
		return nil, err
	}
	return compiledRule, nil
}
