/**
 * @note
 * evaluate
 *
 * <AUTHOR>
 * @date 	2025-09-08
 */
package engine

import (
	"context"
	"gitlab.docsl.com/security/socv2/soc/internal/model/alarm"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

func (rule *CompiledFilterRule) EvaluateFilterRule(ctx context.Context, alarm *alarm.AlarmEvent) (hit bool, err error) {
	if len(rule.filterCondition) == 0 || rule.filterExpression == nil {
		return true, nil
	}
	if alarm.TypeID != rule.BusinessID {
		return false, nil
	}
	result, err := rule.filterExpression.Eval(alarm)
	if err != nil {
		return false, err
	}
	var ok bool
	if hit, ok = result.(bool); !ok {
		return false, socCommon.ErrEvalRuleResultType
	}
	return hit, nil
}

func (rule *CompiledFilterRule) EvaluateDeduplicationRule(ctx context.Context, alarm *alarm.AlarmEvent) (hit bool, err error) {
	return true, nil
}
