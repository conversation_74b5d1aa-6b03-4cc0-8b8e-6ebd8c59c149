/**
 * @note
 * evaluate
 *
 * <AUTHOR>
 * @date 	2025-09-08
 */
package engine

import (
	"context"
	"gitlab.docsl.com/security/socv2/soc/internal/model/alarm"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"slices"
)

func (rule *CompiledFilterRule) EvaluateFilterRule(ctx context.Context, alarm *alarm.AlarmEvent) (hit bool, err error) {
	// 先校验typeID&businessID&tenantID
	if alarm.TypeID != rule.TypeID {
		return false, nil
	}
	if alarm.BusinessID != rule.BusinessID {
		return false, nil
	}
	if !slices.Contains(rule.TenantID, alarm.TenantID) &&
		!slices.Contains(rule.TenantID, socCommon.TenantIDAll) {
		return false, nil
	}

	if len(rule.filterCondition) == 0 || rule.filterExpression == nil {
		return true, nil
	}
	if alarm.TypeID != rule.TypeID {
		return false, nil
	}
	result, err := rule.filterExpression.Eval(alarm)
	if err != nil {
		return false, err
	}
	var ok bool
	if hit, ok = result.(bool); !ok {
		return false, socCommon.ErrEvalRuleResultType
	}
	return hit, nil
}

func (rule *CompiledFilterRule) EvaluateDeduplicationRule(ctx context.Context, alarm *alarm.AlarmEvent) (dedup bool, err error) {
	return true, nil
}
