/**
 * @note
 * filter_rule
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package filter

import (
	"context"
	"errors"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gitlab.docsl.com/security/socv2/soc/pkg/model/op_log"

	"gitlab.docsl.com/security/common"
	bpmModel "gitlab.docsl.com/security/socv2/soc/internal/model/bpm"
	filterModel "gitlab.docsl.com/security/socv2/soc/internal/model/filter"
	"gorm.io/gorm"
)

// wrapFilterRuleTableToItem 将RuleTable转换为FilterRuleItem
func wrapFilterRuleTableToItem(tb *filterModel.RuleTable) *FilterRuleItem {
	if tb == nil {
		return &FilterRuleItem{}
	}
	return &FilterRuleItem{
		RuleID:       tb.RuleID,
		BusinessID:   tb.BusinessID,
		TenantID:     tb.TenantID,
		TypeID:       tb.TypeID,
		Name:         tb.Name,
		Desc:         tb.Desc,
		Type:         tb.Type,
		FilterConfig: tb.FilterConfig,
		DedupConfig:  tb.DedupConfig,
		Status:       tb.Status,
		HasDraft:     tb.Status == socCommon.StatusDraft,
		Version:      tb.Version,
		CreatedAt:    tb.CreatedAt,
		UpdatedAt:    tb.UpdatedAt,
	}
}

/*
QueryLatestFilterRuleList 查询最新版本的过滤规则列表
1. 先查出列表及count。
2. 对列表中的disable状态的rule，去除这批rule的ruleID集合，调用model层的QueryFilterRuleBySeveralConditions，查询这批ruleID对应的草稿态
3. 对于有草稿的disable状态的rule，将草稿的ruleItem覆盖这个disable的RuleItem。
4. 调用model层bmp的查询方法，查询这批ruleID对应的处于processing状态的bpmtable，填入对应的ruleItem
*/
func QueryLatestFilterRuleList(ctx context.Context, page, perPage int, ruleID, businessID, name string, tenantIDs []string, ruleType socCommon.FilterRuleType) (items []*FilterRuleItem, count int64, err error) {
	// 1. 先查出列表及count
	filter := filterModel.FilterRuleQueryFilter{
		Page:       page,
		PerPage:    perPage,
		RuleIDs:    []string{ruleID},
		BusinessID: businessID,
		TenantIDs:  tenantIDs,
		Name:       name,
		Type:       ruleType,
	}
	tbs, err := filterModel.QueryLatestFilterRule(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	count, err = filterModel.QueryLatestFilterRuleCount(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// 转换为FilterRuleItem
	items = make([]*FilterRuleItem, 0, len(tbs))
	ruleIDToItemIndex := make(map[string]int) // ruleID到items索引的映射
	disabledRuleIDs := make([]string, 0)      // 收集disabled状态的ruleID

	for i, tb := range tbs {
		item := wrapFilterRuleTableToItem(tb)
		items = append(items, item)
		ruleIDToItemIndex[tb.RuleID] = i

		// 收集disabled状态的ruleID
		if tb.Status == socCommon.StatusDisabled {
			disabledRuleIDs = append(disabledRuleIDs, tb.RuleID)
		}
	}

	// 2. 对列表中的disable状态的rule，查询这批ruleID对应的草稿态
	if len(disabledRuleIDs) > 0 {
		// 查询这些ruleID的草稿
		draftRules, err := filterModel.QueryFilterRuleBySeveralConditions(ctx, filterModel.FilterRuleQueryFilter{
			RuleIDs: disabledRuleIDs,
			Status:  socCommon.StatusDraft,
		})
		if err != nil {
			return nil, 0, err
		}
		for _, draftRule := range draftRules {
			if itemIndex, exists := ruleIDToItemIndex[draftRule.RuleID]; exists {
				// 检查原规则是否是disabled状态
				if items[itemIndex].Status == socCommon.StatusDisabled {
					// 用草稿覆盖disabled的规则，保留HasDraft标记
					draftItem := wrapFilterRuleTableToItem(draftRule)
					draftItem.Status = items[itemIndex].Status
					draftItem.Version = items[itemIndex].Version
					items[itemIndex] = draftItem
				}
			}
		}
	}

	return items, count, nil
}

// GetFilterRuleDetailByVersion 获取最新版本的过滤规则详情
// 传入ruleID和version，返回FilterRuleItem
func GetFilterRuleDetailByVersion(ctx context.Context, ruleID string, version *int64) (item *FilterRuleItem, err error) {
	// 获取数据库连接
	db, err := filterModel.GetDB(ctx)
	if err != nil {
		return nil, err
	}

	// 1. 查询最新版本的规则
	rules, err := filterModel.QueryFilterRuleBySeveralConditionsWithTx(ctx, db, filterModel.FilterRuleQueryFilter{
		RuleIDs: []string{ruleID},
		Version: version,
	})
	if err != nil {
		return nil, err
	} else if len(rules) == 0 {
		return nil, common.ErrRecordNotFound
	}

	// 转换为FilterRuleItem
	item = wrapFilterRuleTableToItem(rules[0])

	// 2. 查询BPM信息
	bpmFilter := bpmModel.BpmProcessQueryFilter{
		RefIDs:  []string{ruleID},
		RefType: socCommon.BpmRefTypeFilterRule,
		Status:  socCommon.BpmStatusProcessing,
	}

	bpmProcesses, err := bpmModel.QueryBpmProcessBySeveralConditions(ctx, bpmFilter)
	if err != nil {
		return nil, err
	}

	// 填充BPM信息
	if len(bpmProcesses) > 0 {
		item.Bpm = bpmProcesses[0] // 取第一个处理中的BPM记录
	}

	return item, nil
}

// GetLatestFilterRuleDetail 获取最新版本的过滤规则详情
// 传入ruleID，返回FilterRuleItem，无论是disable和enable的状态，都要去查询是否有草稿，有的话，回填到item里
func GetLatestFilterRuleDetail(ctx context.Context, ruleID string) (item *FilterRuleItem, err error) {
	// 获取数据库连接
	db, err := filterModel.GetDB(ctx)
	if err != nil {
		return nil, err
	}

	// 1. 查询最新版本的规则
	latestRule, err := filterModel.QueryLatestFilterRuleByRuleID(ctx, db, ruleID, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, socCommon.ErrFilterRuleNotFound
		}
		return nil, err
	}

	// 转换为FilterRuleItem
	item = wrapFilterRuleTableToItem(latestRule)

	// 2. 无论是disable和enable的状态，都要去查询是否有草稿
	draftRule, err := filterModel.QueryLatestFilterRuleByRuleID(ctx, db, ruleID, []socCommon.FilterRuleStatus{socCommon.StatusDraft})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// 3. 如果有草稿，回填到item里
	if err == nil && draftRule != nil {
		// 用草稿覆盖原规则内容，但保留HasDraft标记
		draftItem := wrapFilterRuleTableToItem(draftRule)
		draftItem.HasDraft = true
		item = draftItem
	}

	// 4. 查询BPM信息
	bpmFilter := bpmModel.BpmProcessQueryFilter{
		RefIDs:  []string{ruleID},
		RefType: socCommon.BpmRefTypeFilterRule,
		Status:  socCommon.BpmStatusProcessing,
	}

	bpmProcesses, err := bpmModel.QueryBpmProcessBySeveralConditions(ctx, bpmFilter)
	if err != nil {
		return nil, err
	}

	// 填充BPM信息
	if len(bpmProcesses) > 0 {
		item.Bpm = bpmProcesses[0] // 取第一个处理中的BPM记录
	}

	return item, nil
}

// 仅为创建草稿
func CreateFilterRule(ctx context.Context, businessID string, tenantID *filterModel.TenantIDArray, name string, desc string, ruleType socCommon.FilterRuleType, filterConfig *filterModel.FilterRuleConfig, dedupConfig *filterModel.DeduplicationRuleConfig) (id int64, err error) {
	tb := &filterModel.RuleTable{
		RuleID:       socCommon.ShortID(),
		BusinessID:   businessID,
		TenantID:     tenantID,
		Name:         name,
		Desc:         desc,
		Type:         ruleType,
		FilterConfig: filterConfig,
		DedupConfig:  dedupConfig,
		Status:       socCommon.StatusDraft,
		Version:      0,
	}
	id, err = filterModel.CreateFilterRule(ctx, tb)
	if err != nil {
		return id, err
	}
	tb.ID = uint(id)
	if err = op_log.CreateOperationLog(ctx, socCommon.OperationFilterRuleCreate, &op_log.OperationDetail{
		Target: tb,
	}); err != nil {
		common.GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	return id, nil
}

/*
EditFilterRule 编辑过滤规则
1. 查询传入的ruleID是否有status=StatusDraft的草稿，如果有，则更新，如果没有，则创建一条新的status=StatusDraft的草稿。
2. 查询传入的ruleID的最新一条（version最大的），如果是Disable状态或者是草稿状态，则方法结束，如果是Enable状态，则调用PublishFilterRule方法，把这条保存的草稿enable了。
*/
func EditFilterRule(ctx context.Context, ruleID string, businessID string, tenantID *filterModel.TenantIDArray, name, desc string, ruleType socCommon.FilterRuleType, filterConfig *filterModel.FilterRuleConfig, dedupConfig *filterModel.DeduplicationRuleConfig) (err error) {
	// 获取数据库连接
	db, err := filterModel.GetDB(ctx)
	if err != nil {
		return err
	}

	latestRule, err := filterModel.QueryLatestFilterRuleByRuleID(ctx, db, ruleID, nil)
	if err != nil {
		return err
	}

	// 开启事务
	if err = db.Transaction(func(tx *gorm.DB) error {
		// 1. 查询传入的ruleID是否有status=StatusDraft的草稿
		draftRule, err := filterModel.QueryLatestFilterRuleByRuleID(ctx, tx, ruleID, []socCommon.FilterRuleStatus{socCommon.StatusDraft})

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		var savedDraft *filterModel.RuleTable
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 没有草稿，创建一条新的status=StatusDraft的草稿
			newDraft := &filterModel.RuleTable{
				RuleID:       ruleID,
				BusinessID:   businessID,
				TenantID:     tenantID,
				Name:         name,
				Desc:         desc,
				Type:         ruleType,
				FilterConfig: filterConfig,
				DedupConfig:  dedupConfig,
				Status:       socCommon.StatusDraft,
				Version:      0, // 草稿版本为0
			}
			newDraft.CreatedAt = latestRule.CreatedAt // 将创建时间与之前的规则统一
			_, err = filterModel.CreateFilterRuleWithTx(ctx, tx, newDraft)
			if err != nil {
				return err
			}
			savedDraft = newDraft
		} else {
			// 有草稿，更新草稿
			err = filterModel.UpdateFilterRuleByIDWithTx(ctx, tx, draftRule.ID, &businessID, tenantID, &name, &desc, &ruleType, filterConfig, dedupConfig, nil, nil)
			if err != nil {
				return err
			}
			savedDraft = draftRule
		}
		// 记录操作日志
		if err = op_log.CreateOperationLog(ctx, socCommon.OperationFilterRuleEdit, &op_log.OperationDetail{
			Target: ruleID,
			Before: wrapFilterRuleTableToItem(latestRule),
			After:  wrapFilterRuleTableToItem(savedDraft),
		}); err != nil {
			common.GetLogger(ctx).Errorf("Create operation log error: %v", err)
		}
		return nil
	}); err != nil {
		return err
	}

	// 3. 如果最新一条是Enable状态，则调用PublishFilterRule方法
	if latestRule.Status == socCommon.StatusEnabled {
		// 调用PublishFilterRule方法，把这条保存的草稿enable了
		return PublishFilterRule(ctx, ruleID)
	}

	return nil
}

/*
PublishFilterRule 发布过滤规则
开启事务，根据查询的ruleID，查询ruleID是否有status=Draft状态的草稿记为draftRule，查询ruleID的最新一条（version最大的）记为latestRule：
 1. 如果draftRule存在，且latestRule也是草稿，将这条草稿更新成Enabled状态，version=1。
 2. 如果draftRule存在，且latestRule不是草稿，则将且latestRule置为Archived，再将draftRule更新成Enabled状态，而且version=latestRule的version+1。
 3. 如果draftRule不存在，且latestRule是Disable状态，则将latestRule置为enable状态
 4. 如果draftRule不存在，且latestRule不存在或不是Disable状态，则报错。
*/
func PublishFilterRule(ctx context.Context, ruleID string) (err error) {
	// 获取数据库连接
	db, err := filterModel.GetDB(ctx)
	if err != nil {
		return err
	}

	oplogFunc := func(rule *filterModel.RuleTable, version int64) {
		if errOpLog := op_log.CreateOperationLog(ctx, socCommon.OperationFilterRulePublish, &op_log.OperationDetail{
			Target: ruleID,
			Before: wrapFilterRuleTableToItem(rule),
			After: &FilterRuleItem{
				RuleID:       rule.RuleID,
				BusinessID:   rule.BusinessID,
				TenantID:     rule.TenantID,
				Name:         rule.Name,
				Desc:         rule.Desc,
				Type:         rule.Type,
				FilterConfig: rule.FilterConfig,
				DedupConfig:  rule.DedupConfig,
				Status:       socCommon.StatusEnabled,
				Version:      version,
			},
		}); errOpLog != nil {
			common.GetLogger(ctx).Errorf("Create operation log error: %v", errOpLog)
		}
	}
	// 开启事务
	return db.Transaction(func(tx *gorm.DB) error {
		// 查询ruleID是否有status=Draft状态的草稿记为draftRule
		draftRule, draftErr := filterModel.QueryLatestFilterRuleByRuleID(ctx, tx, ruleID, []socCommon.FilterRuleStatus{socCommon.StatusDraft})
		if draftErr != nil && !errors.Is(draftErr, gorm.ErrRecordNotFound) {
			return draftErr
		}
		draftExists := draftErr == nil

		// 查询ruleID的最新一条（version最大的）记为latestRule
		latestRule, latestErr := filterModel.QueryLatestFilterRuleByRuleID(ctx, tx, ruleID, nil)
		if latestErr != nil && !errors.Is(latestErr, gorm.ErrRecordNotFound) {
			return latestErr
		}
		latestExists := latestErr == nil

		if draftExists {
			newVersion := int64(1)
			// draftRule存在的情况
			if latestExists && latestRule.Status != socCommon.StatusDraft {
				// 如果draftRule存在，且latestRule不是草稿，则将latestRule状态置为Archived，再将draftRule更新成Enabled状态，而且version=原最新一条的version+1
				// 将原来最新一条状态置为Archived
				archivedStatus := socCommon.StatusArchived
				err = filterModel.UpdateFilterRuleByIDWithTx(ctx, tx, latestRule.ID, nil, nil, nil, nil, nil, nil, nil, &archivedStatus, nil)
				if err != nil {
					return err
				}

				// newVersion=原最新一条的version+1
				newVersion = latestRule.Version + 1
			}
			// 将草稿更新成Enabled状态
			enabledStatus := socCommon.StatusEnabled
			err = filterModel.UpdateFilterRuleByIDWithTx(ctx, tx, draftRule.ID, nil, nil, nil, nil, nil, nil, nil, &enabledStatus, &newVersion)
			if err != nil {
				return err
			}
			oplogFunc(draftRule, newVersion)
		} else if latestExists && latestRule.Status == socCommon.StatusDisabled {
			// 如果draftRule不存在，且latestRule是Disable状态，则将latestRule置为enable状态
			enabledStatus := socCommon.StatusEnabled
			err = filterModel.UpdateFilterRuleByIDWithTx(ctx, tx, latestRule.ID, nil, nil, nil, nil, nil, nil, nil, &enabledStatus, nil)
			if err != nil {
				return err
			}

			// 记录操作日志
			oplogFunc(latestRule, latestRule.Version)
		} else if !latestExists { // 如果draftRule不存在，且latestRule不存在或不是Disable状态，则报错
			return socCommon.ErrFilterRuleNotFound
		} else {
			return socCommon.ErrInvalidFilterRuleStatus
		}

		return nil
	})
}

// DisableFilterRule 禁用过滤规则
// 先判断LatestRule是不是Enabled的状态，如果是，更新成Disabled的状态即可。如果不是Enabled的状态，则报错。
func DisableFilterRule(ctx context.Context, ruleID string) (err error) {
	// 获取数据库连接
	db, err := filterModel.GetDB(ctx)
	if err != nil {
		return err
	}

	// 查询ruleID的最新一条（version最大的）记为latestRule
	latestRule, err := filterModel.QueryLatestFilterRuleByRuleID(ctx, db, ruleID, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return socCommon.ErrFilterRuleNotFound
		}
		return err
	}

	// 判断最新规则是否是Enabled状态
	if latestRule.Status != socCommon.StatusEnabled {
		return socCommon.ErrInvalidFilterRuleStatus
	}

	// 将规则状态更新为Disabled
	disabledStatus := socCommon.StatusDisabled
	err = filterModel.UpdateFilterRuleByIDWithTx(ctx, db, latestRule.ID, nil, nil, nil, nil, nil, nil, nil, &disabledStatus, nil)
	if err != nil {
		return err
	}

	// 记录操作日志
	if errOpLog := op_log.CreateOperationLog(ctx, socCommon.OperationFilterRuleUpdate, &op_log.OperationDetail{
		Target: ruleID,
		Before: wrapFilterRuleTableToItem(latestRule),
		After: &FilterRuleItem{
			RuleID:       latestRule.RuleID,
			BusinessID:   latestRule.BusinessID,
			TenantID:     latestRule.TenantID,
			Name:         latestRule.Name,
			Desc:         latestRule.Desc,
			Type:         latestRule.Type,
			FilterConfig: latestRule.FilterConfig,
			DedupConfig:  latestRule.DedupConfig,
			Status:       socCommon.StatusDisabled,
			Version:      latestRule.Version,
		},
	}); errOpLog != nil {
		common.GetLogger(ctx).Errorf("Create operation log error: %v", errOpLog)
	}
	return nil
}

// DeleteFilterRule 删除过滤规则
func DeleteFilterRule(ctx context.Context, ruleID string) (err error) {
	// 查询原始数据用于日志记录
	originalFilterRule, err := filterModel.QueryFilterRuleBySeveralConditions(ctx, filterModel.FilterRuleQueryFilter{
		RuleIDs: []string{ruleID},
		Status:  socCommon.StatusEnabled, // 存在启用状态的过滤规则不允许删除
	})
	if err != nil {
		return err
	}
	if len(originalFilterRule) > 0 {
		return socCommon.ErrDeleteEnabledFilterRule
	}

	err = filterModel.DeleteFilterRule(ctx, ruleID)
	if err != nil {
		return err
	}

	// 记录操作日志
	if err = op_log.CreateOperationLog(ctx, socCommon.OperationFilterRuleDelete, &op_log.OperationDetail{
		Target: ruleID,
	}); err != nil {
		common.GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}

	return nil
}
