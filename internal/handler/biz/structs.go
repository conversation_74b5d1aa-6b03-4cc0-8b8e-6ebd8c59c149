/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import (
	bizLogic "gitlab.docsl.com/security/socv2/soc/internal/logic/biz"
)

type ListTenantRequest struct {
	Page      int      `json:"page" validate:"gte=0"`
	PerPage   int      `json:"perPage" validate:"gt=0"`
	TenantIDs []string `json:"tenantIDs"`
	Name      string   `json:"name"`
}

type ListTenantResponse struct {
	Total int64                  `json:"total"`
	Items []*bizLogic.TenantItem `json:"items"`
}

type CreateTenantRequest struct {
	TenantID   string `json:"tenantID" validate:"required"`
	Name       string `json:"name" validate:"required"`
	Desc       string `json:"desc"`
	ExchangeID int64  `json:"exchangeID"`
}

type CreateTenantResponse struct {
	ID uint `json:"id"`
}

type UpdateTenantRequest struct {
	TenantID   string  `json:"tenantID" validate:"required"`
	Name       *string `json:"name"`
	Desc       *string `json:"desc"`
	ExchangeID *int64  `json:"exchangeID"`
}

type DeleteTenantRequest struct {
	TenantIDs []string `json:"tenantIDs" validate:"required"`
}

// Biz相关结构体
type ListBizRequest struct {
	Page             int    `json:"page" validate:"gte=0"`
	PerPage          int    `json:"perPage" validate:"gt=0"`
	BusinessID       string `json:"businessID"`
	Name             string `json:"name"`
	OriginBusinessID int64  `json:"originBusinessID"`
}

type ListBizResponse struct {
	Total int64               `json:"total"`
	Items []*bizLogic.BizItem `json:"items"`
}

type CreateBizRequest struct {
	BusinessID       string `json:"businessID" validate:"required"`
	Name             string `json:"name" validate:"required"`
	Desc             string `json:"desc"`
	OriginBusinessID int64  `json:"originBusinessID"`
}

type CreateBizResponse struct {
	ID uint `json:"id"`
}

type UpdateBizRequest struct {
	BusinessID       string  `json:"businessID" validate:"required"`
	Name             *string `json:"name"`
	Desc             *string `json:"desc"`
	OriginBusinessID *int64  `json:"originBusinessID"`
}

type DeleteBizRequest struct {
	BusinessID string `json:"businessID" validate:"required"`
}

// AlarmType相关结构体
type ListAlarmTypeRequest struct {
	Page         int    `json:"page" validate:"gte=0"`
	PerPage      int    `json:"perPage" validate:"gt=0"`
	TypeID       string `json:"typeID"`
	BusinessID   string `json:"businessID"`
	Name         string `json:"name"`
	OriginTypeID int64  `json:"originTypeID"`
}

type ListAlarmTypeResponse struct {
	Total int64                     `json:"total"`
	Items []*bizLogic.AlarmTypeItem `json:"items"`
}

type CreateAlarmTypeRequest struct {
	TypeID       string `json:"typeID" validate:"required"`
	BusinessID   string `json:"businessID" validate:"required"`
	Name         string `json:"name" validate:"required"`
	Desc         string `json:"desc"`
	OriginTypeID int64  `json:"originTypeID"`
}

type CreateAlarmTypeResponse struct {
	ID uint `json:"id"`
}

type UpdateAlarmTypeRequest struct {
	TypeID       string  `json:"typeID" validate:"required"`
	BusinessID   *string `json:"businessID"`
	Name         *string `json:"name"`
	Desc         *string `json:"desc"`
	OriginTypeID *int64  `json:"originTypeID"`
}

type DeleteAlarmTypeRequest struct {
	TypeID string `json:"typeID" validate:"required"`
}
