package filter

import (
	"github.com/kataras/iris/v12"

	. "gitlab.docsl.com/security/common"
	bpmLogic "gitlab.docsl.com/security/socv2/soc/internal/logic/bpm"
	filterLogic "gitlab.docsl.com/security/socv2/soc/internal/logic/filter"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

func QueryFilterRuleList(ctx iris.Context) {
	req := &ListLatestFilterRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var err error
	ret := &ListLatestFilterRuleResponse{}
	ret.Items, ret.Total, err = filterLogic.QueryLatestFilterRuleList(ctx, req.Page, req.PerPage,
		req.RuleID, req.BusinessID, req.Name, req.TenantIDs, req.Type)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrQueryFilterRuleList, err))
		return
	}
	allRuleIDs := make([]string, 0, len(ret.Items)) // 收集所有ruleID
	ruleIDToItemIndex := make(map[string]int)       // ruleID到items索引的映射
	for i, item := range ret.Items {
		allRuleIDs = append(allRuleIDs, item.RuleID)
		ruleIDToItemIndex[item.RuleID] = i
	}
	// 调用model层bpm的查询方法，查询这批ruleID对应的处于processing状态的bpmTable，填入对应的ruleItem
	if len(allRuleIDs) > 0 {
		bmpProcesses, err := bpmLogic.QueryProcessingBpmProcessListByRefIDsAndRefType(ctx, allRuleIDs, socCommon.BpmRefTypeFilterRule)
		if err != nil {
			SetRet(ctx, NewError(socCommon.ErrQueryFilterRuleList, err))
			return
		}
		// 将BMP信息填入对应的ruleItem
		for _, bmpProcess := range bmpProcesses {
			if itemIndex, exists := ruleIDToItemIndex[bmpProcess.RefID]; exists {
				ret.Items[itemIndex].Bpm = bmpProcess
			}
		}
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

func CreateFilterRule(ctx iris.Context) {
	req := &CreateFilterRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	_, err := filterLogic.CreateFilterRule(ctx, req.BusinessID, req.TenantID, req.Name, req.Desc, req.Type, req.FilterConfig, req.DedupConfig)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrCreateFilterRule, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func DeleteFilterRule(ctx iris.Context) {
	req := &DeleteFilterRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := filterLogic.DeleteFilterRule(ctx, req.RuleID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrDeleteFilterRule, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func PublishFilterRule(ctx iris.Context) {
	req := &PublishFilterRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := filterLogic.PublishFilterRule(ctx, req.RuleID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrPublishFilterRule, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func DisableFilterRule(ctx iris.Context) {
	req := &DisableFilterRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := filterLogic.DisableFilterRule(ctx, req.RuleID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrPublishFilterRule, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func EditFilterRule(ctx iris.Context) {
	req := &EditFilterRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := filterLogic.EditFilterRule(ctx, req.RuleID, req.BusinessID, req.TenantID, req.Name, req.Desc, req.Type, req.FilterConfig, req.DedupConfig)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrEditFilterRule, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func GetFilterRuleDetail(ctx iris.Context) {
	req := &GetFilterRuleDetailRequest{}
	errJson := ctx.ReadQuery(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var item *filterLogic.FilterRuleItem
	var err error
	if req.Version != nil {
		item, err = filterLogic.GetFilterRuleDetailByVersion(ctx, req.RuleID, req.Version)
	} else {
		item, err = filterLogic.GetLatestFilterRuleDetail(ctx, req.RuleID)
	}
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrQueryFilterRule, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(item))
}
