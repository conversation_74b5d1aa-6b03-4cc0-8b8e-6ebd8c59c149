/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package filter

import (
	filterLogic "gitlab.docsl.com/security/socv2/soc/internal/logic/filter"
	filterModel "gitlab.docsl.com/security/socv2/soc/internal/model/filter"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

type ListLatestFilterRuleRequest struct {
	Page       int                      `json:"page" validate:"gte=0"`
	PerPage    int                      `json:"perPage" validate:"gt=0"`
	RuleID     string                   `json:"ruleID"`
	BusinessID string                   `json:"businessID"`
	TenantIDs  []string                 `json:"tenantIDs"`
	Name       string                   `json:"name"`
	Type       socCommon.FilterRuleType `json:"type"`
}

type ListLatestFilterRuleResponse struct {
	Total int64                         `json:"total"`
	Items []*filterLogic.FilterRuleItem `json:"items"`
}

type CreateFilterRuleRequest struct {
	RuleID       string                               `json:"ruleID" validate:"required"`
	BusinessID   string                               `json:"businessID" validate:"required"`
	TenantID     *filterModel.TenantIDArray           `json:"tenantID"`
	Name         string                               `json:"name" validate:"required"`
	Desc         string                               `json:"desc"`
	Type         socCommon.FilterRuleType             `json:"type" validate:"required"`
	FilterConfig *filterModel.FilterRuleConfig        `json:"filterConfig"`
	DedupConfig  *filterModel.DeduplicationRuleConfig `json:"dedupConfig"`
	Status       socCommon.FilterRuleStatus           `json:"status" validate:"required"`
	Version      int64                                `json:"version" validate:"required"`
}

type CreateFilterRuleResponse struct {
	ID uint `json:"id"`
}

type UpdateFilterRuleRequest struct {
	RuleID       string                               `json:"ruleID" validate:"required"`
	BusinessID   *string                              `json:"businessID"`
	TenantID     *filterModel.TenantIDArray           `json:"tenantID"`
	Name         *string                              `json:"name"`
	Desc         *string                              `json:"desc"`
	Type         *socCommon.FilterRuleType            `json:"type"`
	FilterConfig *filterModel.FilterRuleConfig        `json:"filterConfig"`
	DedupConfig  *filterModel.DeduplicationRuleConfig `json:"dedupConfig"`
	Status       *socCommon.FilterRuleStatus          `json:"status"`
	Version      *int64                               `json:"version"`
}

type DeleteFilterRuleRequest struct {
	RuleID string `json:"ruleID" validate:"required"`
}

type PublishFilterRuleRequest struct {
	RuleID string `json:"ruleID" validate:"required"`
}

type DisableFilterRuleRequest struct {
	RuleID string `json:"ruleID" validate:"required"`
}

type EditFilterRuleRequest struct {
	RuleID       string                               `json:"ruleID" validate:"required"`
	BusinessID   string                               `json:"businessID" validate:"required"`
	TenantID     *filterModel.TenantIDArray           `json:"tenantID"`
	Name         string                               `json:"name" validate:"required"`
	Desc         string                               `json:"desc"`
	Type         socCommon.FilterRuleType             `json:"type" validate:"required"`
	FilterConfig *filterModel.FilterRuleConfig        `json:"filterConfig"`
	DedupConfig  *filterModel.DeduplicationRuleConfig `json:"dedupConfig"`
}

type GetFilterRuleDetailRequest struct {
	RuleID  string `json:"ruleID" validate:"required"`
	Version *int64 `json:"version"`
}
