/**
 * @note
 * main.go
 *
 * <AUTHOR>
 * @date 	2025-01-23
 */
package main

import (
	"context"
	"flag"
	"fmt"
	"gitlab.docsl.com/security/common/cas"
	"gitlab.docsl.com/security/socv2/soc/internal/handler/biz"
	"gitlab.docsl.com/security/socv2/soc/internal/handler/filter"
	"log"
	"os"

	"github.com/kataras/iris/v12"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/tmsong/hlog"

	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/http_server"
	"gitlab.docsl.com/security/common/idgen"
	"gitlab.docsl.com/security/common/logger"
	"gitlab.docsl.com/security/common/masker"
	"gitlab.docsl.com/security/common/prometheus"
	"gitlab.docsl.com/security/common/redis"

	"gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gitlab.docsl.com/security/socv2/soc/pkg/config"
)

/**
 * @note
 * 运行选项
 */
func Flagset() *flag.FlagSet {
	flagSet := flag.NewFlagSet(common.ModuleName, flag.ExitOnError)
	flagSet.String("config", "", "path to config file")
	flagSet.String("logfile", "", "log output file")
	flagSet.String("outputConfig", "", "output config file")
	flagSet.String("encrypt", "", "encrypt config file")

	return flagSet
}

func main() {
	////////////////////CONFIG LOAD////////////////////

	masker.SetGetKeyFunc(func(arg string) []byte {
		return []byte(os.Getenv("AUDIT_CONFIG_CRYPT_KEY"))
	})

	//读取运行参数
	flagSet := Flagset()
	flagSet.Parse(os.Args[1:])

	//读取配置
	outputConfig := flagSet.Lookup("outputConfig").Value.String()
	encrypt := flagSet.Lookup("encrypt").Value.String() == "true"
	configFile := flagSet.Lookup("config").Value.String()
	if configFile != "" {
		configFile, err := config.ReplaceAndLoad(configFile, !encrypt)
		if err != nil {
			log.Fatalf("ERROR: failed to load config file %s - %s\n", configFile, err)
		}
	} else {
		log.Fatalln("ERROR: must set config file")
	}

	// 这里用来生成加密或解密配置文件，平时用不到
	if outputConfig != StringEmpty {
		if err := config.OutputConfigToFile(outputConfig, encrypt); err != nil {
			panic(err)
		}
		return
	}

	//取日志配置，如果没有，使用配置文件中的默认配置
	logFile := flagSet.Lookup("logfile").Value.String()
	if logFile != StringEmpty {
		logger.SetLoggerFile(logFile)
	}

	////////////////////SERVICE INIT////////////////////

	//崩溃恢复打印
	defer func() {
		if err := recover(); err != nil {
			log.Printf("ERROR: abort, unknown error, reason:%v,\n stack:%s\n", err, IdentifyPanic())
			fmt.Printf("ERROR: abort, unknown error, reason:%v", err)
		}
	}()

	// 初始化日志组件&ctx
	loggerConfig := logger.GetHlogConfig()

	// 初始化根logger
	l := hlog.NewLoggerWithConfig(loggerConfig, 0)

	// 初始化根ctx用于service
	ctx, cancel := context.WithCancel(context.Background())
	ctx = context.WithValue(ctx, KeyLogger, l)

	// 初始化IDGen
	defaultRedisClient, err := redis.DefaultClient(ctx)
	if err != nil {
		log.Fatalf("ERROR: failed to get default redis client, reason:%v\n", err)
	}
	idgen.Init(defaultRedisClient, 10)

	// 脱敏
	masker.SetMask(true)

	// Pprof
	if http_server.GetConfig().Debug {
		StartPProf(http_server.GetConfig().PProfAddr)
		log.Println("start pprof, addr:", http_server.GetConfig().PProfAddr)
	}

	wg := &WaitGroupWrapper{}

	// 初始化server实例
	engine := http_server.CustomServer(l, true)

	// prometheus实例
	promEngine := iris.New()
	promEngine.Get("/metrics", iris.FromStd(promhttp.Handler()))

	// 注册接口
	engine.UseRouter(cas.ValidateSSO())
	apiParty := engine.Party("/api")

	WrapFilterRulesApi(apiParty)
	WrapTenantApi(apiParty)
	WrapBusinessApi(apiParty)
	WrapAlarmTypeApi(apiParty)

	////////////////////SERVICE START////////////////////
	log.Println("service start")

	// 启动web服务
	wg.Wrap(func() {
		err := engine.Listen(http_server.GetConfig().InterAddr, iris.WithoutInterruptHandler)
		if err != nil {
			log.Println("service start error:", err)
		}
	})

	// 启动prometheus
	wg.Wrap(func() {
		log.Println("start prometheus, addr:", prometheus.GetPrometheusConfig().Addr)
		err := promEngine.Listen(prometheus.GetPrometheusConfig().Addr, iris.WithoutInterruptHandler)
		if err != nil {
			log.Println("prometheus start error:", err)
		}
	})

	////////////////////EXIT CONTROL////////////////////

	// 监听QuitChan，控制idGen注销，日志实例退出
	wg.Wrap(func() {
		<-QuitChan
		cancel()
		idgen.UnRegister()
		log.Println("idgen unregistered")
		l.Close()
		log.Println("logger closed")
	})

	// 优雅关闭server，并关闭QuitChan
	http_server.GracefulExit(engine, promEngine)
	log.Println("service stopping...")

	// 等待server,日志实例全部退出
	wg.Wait()

	// 退出pprof
	if http_server.GetConfig().Debug {
		StopPProf()
	}

	// 服务关闭
	log.Println("service stopped")
}

func WrapFilterRulesApi(app iris.Party) {
	app.Post("/filter/rule/list", filter.QueryFilterRuleList)
	app.Get("/filter/rule/detail", filter.GetFilterRuleDetail)
	app.Post("/filter/rule/create", filter.CreateFilterRule)
	app.Post("/filter/rule/edit", filter.EditFilterRule)
	app.Post("/filter/rule/publish", filter.PublishFilterRule)
	app.Post("/filter/rule/disable", filter.DisableFilterRule)
}

func WrapTenantApi(app iris.Party) {
	app.Post("/tenant/list", biz.QueryTenantList)
	app.Post("/tenant/create", biz.CreateTenant)
	app.Post("/tenant/update", biz.UpdateTenant)
	app.Post("/tenant/delete", biz.DeleteTenants)
}

func WrapBusinessApi(app iris.Party) {
	app.Post("/business/list", biz.QueryBizList)
	app.Post("/business/create", biz.CreateBiz)
	app.Post("/business/update", biz.UpdateBiz)
	app.Post("/business/delete", biz.DeleteBiz)
}

func WrapAlarmTypeApi(app iris.Party) {
	app.Post("/alarmType/list", biz.QueryAlarmTypeList)
	app.Post("/alarmType/create", biz.CreateAlarmType)
	app.Post("/alarmType/update", biz.UpdateAlarmType)
	app.Post("/alarmType/delete", biz.DeleteAlarmType)
}
